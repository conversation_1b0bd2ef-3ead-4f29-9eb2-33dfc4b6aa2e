import React from 'react';
import { Popover, Table, Typography } from 'antd';
import { DollarSign } from 'lucide-react';

const { Text } = Typography;

const PriceInfo = ({ 
  pricingList = [], 
  displayStyle = 'default', 
  placement = 'top',
  trigger = 'hover',
  children 
}) => {
  // 构建价格表格数据
  const getPriceTableData = () => {
    return pricingList.map(price => {
      const hourlyPrice = price.pricePerUnit || 0;
      const dailyPrice = hourlyPrice * 24;
      const monthlyPrice = dailyPrice * 30;
      const yearlyPrice = dailyPrice * 365;

      return {
        key: `${price.resourceType}-${price.id}`,
        resourceType: price.resourceType === 'GPU' ? 'GPU' : '存储',
        unit: (
          <div className="flex flex-col items-center">
            {price.pricingUnit}
            {price.pricingUnit !== price.pricingUnitDesc &&
              !!price.pricingUnitDesc && (
                <span className="text-xs text-gray-500">
                  {price.pricingUnitDesc}
                </span>
              )}
          </div>
        ),
        hourly: hourlyPrice.toFixed(2),
        daily: dailyPrice.toFixed(2),
        monthly: monthlyPrice.toFixed(2),
        yearly: yearlyPrice.toFixed(2),
      };
    });
  };

  // 表格列配置
  const priceTableColumns = [
    {
      title: '资源类型',
      dataIndex: 'resourceType',
      key: 'resourceType',
      width: 80,
    },
    { 
      title: '单位', 
      dataIndex: 'unit', 
      key: 'unit', 
      width: 100 
    },
    {
      title: '小时',
      dataIndex: 'hourly',
      key: 'hourly',
      width: 70,
      render: val => (
        <span className="font-medium text-blue-600">¥{val}</span>
      ),
    },
    {
      title: '日',
      dataIndex: 'daily',
      key: 'daily',
      width: 70,
      render: val => (
        <span className="text-green-600">¥{val}</span>
      ),
    },
    {
      title: '月',
      dataIndex: 'monthly',
      key: 'monthly',
      width: 80,
      render: val => (
        <span className="text-orange-600">¥{val}</span>
      ),
    },
    {
      title: '年',
      dataIndex: 'yearly',
      key: 'yearly',
      width: 80,
      render: val => (
        <span className="text-red-600">¥{val}</span>
      ),
    },
  ];

  // 悬浮窗内容
  const priceTooltipContent = (
    <div style={{ width: 450 }}>
      <div className="mb-4 pb-2 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <DollarSign className="w-4 h-4 text-blue-500" />
          <span className="text-sm font-medium text-gray-800">价格详情</span>
        </div>
      </div>
      
      <Table
        columns={priceTableColumns}
        dataSource={getPriceTableData()}
        pagination={false}
        size="small"
        className="mb-3"
        rowClassName="hover:bg-blue-50"
      />
      
      <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
        <div className="flex items-center justify-between mb-1">
          <span>💡 计费说明：</span>
        </div>
        <div>• 月按30天计算，年按365天计算</div>
        <div>• 实际计费以系统为准</div>
        <div>• 支持按小时灵活计费</div>
      </div>
    </div>
  );

  // 获取主要显示的价格（通常是GPU价格）
  const primaryPrice = pricingList.find(p => p.resourceType === 'GPU') || pricingList[0];

  // 如果传入了自定义children，直接使用
  if (children) {
    return pricingList.length > 0 ? (
      <Popover
        content={priceTooltipContent}
        placement={placement}
        trigger={trigger}
        overlayClassName="price-info-popover"
      >
        {children}
      </Popover>
    ) : children;
  }

  // 默认显示样式
  if (!primaryPrice) {
    return (
      <div className="text-center">
        <Text type="secondary" className="text-xs">暂无价格信息</Text>
      </div>
    );
  }

  const hourlyPrice = primaryPrice.pricePerUnit;
  const dailyPrice = hourlyPrice * 24;

  // 根据displayStyle返回不同的显示组件
  switch (displayStyle) {
    case 'compact':
      return (
        <Popover
          content={priceTooltipContent}
          placement={placement}
          trigger={trigger}
        >
          <div className="cursor-pointer hover:bg-blue-50 p-2 rounded-lg transition-all">
            <div className="text-lg font-bold text-blue-600">
              ¥{hourlyPrice.toFixed(2)}
            </div>
            <Text type="secondary" className="text-xs">
              {primaryPrice.pricingUnitDesc}
            </Text>
          </div>
        </Popover>
      );
      
    case 'detailed':
      return (
        <Popover
          content={priceTooltipContent}
          placement={placement}
          trigger={trigger}
        >
          <div className="text-center cursor-pointer hover:bg-blue-50 p-3 rounded-lg transition-all">
            <div className="text-xl font-bold text-blue-600 mb-1">
              ¥{hourlyPrice.toFixed(2)}
            </div>
            <Text type="secondary" className="text-xs block mb-1">
              {primaryPrice.pricingUnitDesc}
            </Text>
            <Text type="secondary" className="text-xs">
              日费用: ¥{dailyPrice.toFixed(2)}
            </Text>
            <div className="text-xs text-blue-500 mt-1">
              悬浮查看详情
            </div>
          </div>
        </Popover>
      );
      
    default:
      return (
        <Popover
          content={priceTooltipContent}
          placement={placement}
          trigger={trigger}
        >
          <div className="cursor-pointer hover:bg-gray-50 p-2 rounded transition-all">
            <div className="text-sm font-medium text-orange-600">
              ¥{hourlyPrice.toFixed(2)} 起
            </div>
            <div className="text-xs text-gray-500">
              {primaryPrice.pricingUnitDesc}
            </div>
            <div className="text-xs text-blue-500">
              悬浮查看详情
            </div>
          </div>
        </Popover>
      );
  }
};

export default PriceInfo;