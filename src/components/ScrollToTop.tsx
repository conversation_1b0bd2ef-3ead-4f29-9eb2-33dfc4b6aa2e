import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollToTopProps {
  /**
   * 滚动容器的选择器，如果不提供则滚动到页面顶部
   */
  scrollContainerSelector?: string;
  /**
   * 是否启用平滑滚动
   */
  smooth?: boolean;
}

const ScrollToTop: React.FC<ScrollToTopProps> = ({ 
  scrollContainerSelector,
  smooth = false 
}) => {
  const location = useLocation();

  useEffect(() => {
    // 在路由变化时滚动到顶部
    const scrollToTop = () => {
      if (scrollContainerSelector) {
        // 滚动指定的容器
        const container = document.querySelector(scrollContainerSelector);
        if (container) {
          container.scrollTo({
            top: 0,
            behavior: smooth ? 'smooth' : 'auto'
          });
        }
      } else {
        // 滚动整个页面
        window.scrollTo({
          top: 0,
          behavior: smooth ? 'smooth' : 'auto'
        });
      }
    };

    // 使用 setTimeout 确保在路由切换完成后执行滚动
    const timeoutId = setTimeout(scrollToTop, 0);

    return () => clearTimeout(timeoutId);
  }, [location.pathname, scrollContainerSelector, smooth]);

  return null;
};

export default ScrollToTop;
