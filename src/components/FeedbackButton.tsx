import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { MessageCircle, Table2Icon } from 'lucide-react';
import { cn } from '@/lib/utils';

const FeedbackButton: React.FC = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [isTableHovered, setIsTableHovered] = useState(false);

  const handleFeedbackClick = () => {
    // 打开新窗口，后续可以替换为实际的反馈表单URL
    window.open('https://vcnke18f8ujn.feishu.cn/share/base/form/shrcnPb6j5REGStHsM2tSgDPoEh', '_blank', 'width=600,height=700,scrollbars=yes,resizable=yes');
  };

  const handleCheckTableClick = () => {
    window.open('https://vcnke18f8ujn.feishu.cn/wiki/SDCqwlii8iux6Oki4BIcPnsNned?from=from_copylink', '_blank')
  }

  return (
    <>
      <div className="fixed bottom-6 right-4 z-50">
        <Button
          onClick={handleFeedbackClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className={cn(
            "rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-300",
            "bg-primary hover:bg-primary/90 text-white",
            "flex items-center justify-center group"
          )}
        >
          <MessageCircle className="w-6 h-6" />

          {/* 悬浮提示 */}
          <div className={cn(
            "absolute right-full mr-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap transition-all duration-200",
            "opacity-0 translate-x-2 pointer-events-none",
            isHovered && "opacity-100 translate-x-0"
          )}>
            意见反馈
            {/* 箭头 */}
            <div className="absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
          </div>
        </Button>
        <Button
          onClick={handleCheckTableClick}
          onMouseEnter={() => setIsTableHovered(true)}
          onMouseLeave={() => setIsTableHovered(false)}
          className={cn(
            "rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-300",
            "bg-primary hover:bg-primary/90 text-white",
            "flex items-center justify-center group mt-2"
          )}
        >
          <Table2Icon className="w-6 h-6" />

          {/* 悬浮提示 */}
          <div className={cn(
            "absolute right-full mr-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap transition-all duration-200",
            "opacity-0 translate-x-2 pointer-events-none",
            isTableHovered && "opacity-100 translate-x-0"
          )}>
            查看反馈收集表
            {/* 箭头 */}
            <div className="absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
          </div>
        </Button>
      </div></>
  );
};

export default FeedbackButton;
