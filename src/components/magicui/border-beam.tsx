"use client";

import { cn } from "@/lib/utils";

interface BorderBeamProps {
  className?: string;
  size?: number;
  duration?: number;
  delay?: number;
  colorFrom?: string;
  colorTo?: string;
  reverse?: boolean;
  initialOffset?: number;
  borderWidth?: number;
}

export const BorderBeam = ({
  className,
  size = 50,
  duration = 6,
  delay = 0,
  colorFrom = "#0d22ef",
  colorTo = "#9c40ff",
  reverse = false,
  borderWidth = 1,
}: BorderBeamProps) => {
  return (
    <div
      className={cn(
        "pointer-events-none absolute inset-0 rounded-[inherit] overflow-hidden",
        className
      )}
    >
      {/* 简化版边框光效 */}
      <div
        className="absolute inset-0 rounded-[inherit] animate-pulse"
        style={{
          background: `linear-gradient(90deg, transparent, ${colorFrom}20, ${colorTo}20, transparent)`,
          border: `${borderWidth}px solid transparent`,
          backgroundImage: `linear-gradient(90deg, ${colorFrom}, ${colorTo})`,
          backgroundClip: 'border-box',
          WebkitBackgroundClip: 'border-box',
        }}
      />

      {/* 旋转光效 */}
      {/* <div
        className="absolute inset-0 rounded-[inherit] animate-spin"
        style={{
          background: `conic-gradient(from 0deg, transparent, ${colorFrom}, ${colorTo}, transparent)`,
          mask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,
          WebkitMask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,
          maskComposite: 'xor',
          WebkitMaskComposite: 'xor',
          padding: `${borderWidth}px`,
          animationDuration: `${duration}s`,
          animationDelay: `${delay}s`,
        }}
      /> */}
    </div>
  );
};
