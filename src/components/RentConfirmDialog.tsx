import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Switch, Badge } from 'antd';
import { Device, Application } from '@/data/mockData';
import { formatCurrency } from '@/lib/utils';
import {
  Server,
  Cpu,
  HardDrive,
  Zap,
  MapPin,
  Star,
  User,
  CreditCard,
  Clock,
  CheckCircle,
  Sparkles
} from 'lucide-react';

interface RentConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  device: Device | null;
  selectedApplication?: Application | null;
  onConfirm: (device: Device, autoInstallApp: boolean) => void;
}

const RentConfirmDialog: React.FC<RentConfirmDialogProps> = ({
  isOpen,
  onClose,
  device,
  selectedApplication,
  onConfirm
}) => {
  const [autoInstallApp, setAutoInstallApp] = useState(true);

  if (!device) return null;

  const devicePrice = device.pricePerHour;
  const appPrice = selectedApplication?.pricePerHour || 0;
  const totalPrice = devicePrice + (autoInstallApp && selectedApplication ? appPrice : 0);

  const handleConfirm = () => {
    onConfirm(device, autoInstallApp && !!selectedApplication);
    onClose();
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <Server className="w-5 h-5" />
          确认订购设备
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={800}
    >

      <div className="space-y-6">
        {/* 用户信息 */}
        {/* <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">当前用户</h3>
                  <p className="text-sm text-gray-500"><EMAIL></p>
                </div>
              </div>
            </CardContent>
          </Card> */}

        {/* 设备信息 */}
        <Card title="设备信息" size="small">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="font-semibold text-lg">{device.name}</h3>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{device.location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium">{device.rating}</span>
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                提供商: {device.provider}
              </div>
            </div>
            <Badge color={device.type === 'GPU' ? 'blue' : 'default'}>
              {device.type}
            </Badge>
          </div>

          {/* 设备规格 */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Cpu className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-gray-500">CPU</div>
                <div className="font-medium">{device.cpu}</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <HardDrive className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-gray-500">内存</div>
                <div className="font-medium">{device.memory}</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-gray-500">GPU</div>
                <div className="font-medium">{device.gpu}</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Server className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-gray-500">存储</div>
                <div className="font-medium">{device.storage}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 应用信息 */}
      {selectedApplication && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="text-2xl">{selectedApplication.icon}</div>
                <div>
                  <h3 className="font-medium">{selectedApplication.name}</h3>
                  <p className="text-sm text-gray-500">{selectedApplication.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">{appPrice > 0 ? '订购并安装' : '自动安装'}</span>
                <Switch
                  checked={autoInstallApp}
                  onCheckedChange={setAutoInstallApp}
                />
              </div>
            </div>

            {autoInstallApp && (
              <div className="bg-blue-50 rounded-lg p-3 mt-3">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">将自动安装应用</span>
                </div>
                <p className="text-xs text-blue-700">
                  系统将在设备启动后自动安装并配置 {selectedApplication.name}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 价格信息 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <CreditCard className="w-5 h-5 text-gray-400" />
            <h3 className="font-medium">费用明细</h3>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">设备订购费用</span>
              <span className="font-medium">{formatCurrency(devicePrice)}/小时</span>
            </div>

            {selectedApplication && autoInstallApp && selectedApplication.pricePerHour && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">应用订购费用</span>
                <span className="font-medium">{formatCurrency(appPrice)}/小时</span>
              </div>
            )}

            <div className="border-t pt-2">
              <div className="flex justify-between items-center">
                <span className="font-medium">总计费用</span>
                <span className="text-lg font-bold text-primary">
                  {formatCurrency(totalPrice)}/小时
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 mt-3 p-2 bg-yellow-50 rounded-lg">
            <Clock className="w-4 h-4 text-yellow-600" />
            <span className="text-xs text-yellow-700">
              费用将按实际使用时间计算，最小计费单位为1分钟
            </span>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex gap-3">
        <Button variant="outline" onClick={onClose} className="flex-1">
          取消
        </Button>
        <Button onClick={handleConfirm} className="flex-1">
          <Sparkles className="w-4 h-4 mr-2" />
          确认订购
        </Button>
      </div>
    </div>
    </Modal >
  );
};

export default RentConfirmDialog;
