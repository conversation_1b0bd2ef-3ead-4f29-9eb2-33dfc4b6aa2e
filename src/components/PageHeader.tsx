import { Typography } from 'antd';
import React from 'react';

const { Title, Text } = Typography;

export default function PageHeader({ title, subtitle, children }: { title: string, subtitle: string, children?: React.ReactNode }) {
  return (
    <div className="mb-6 flex items-end">
      <div className='mr-auto'>
        <Title level={2} className="!mb-1 !text-2xl !font-bold text-gray-800">
          {title}
        </Title>
        <Text type="secondary">{subtitle}</Text>
      </div>
      {children}
    </div>
  );
}