"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface SegmentedControlOption {
  value: string
  label: string
  icon?: React.ReactNode
}

interface SegmentedControlProps {
  options: SegmentedControlOption[]
  value: string
  onValueChange: (value: string) => void
  className?: string
  size?: "sm" | "md" | "lg"
}

const SegmentedControl = React.forwardRef<
  HTMLDivElement,
  SegmentedControlProps
>(({ options, value, onValueChange, className, size = "md" }, ref) => {
  const sizeClasses = {
    sm: "h-8 text-xs",
    md: "h-9 text-sm",
    lg: "h-10 text-base"
  }

  const paddingClasses = {
    sm: "p-1",
    md: "p-1",
    lg: "p-1.5"
  }

  const itemPaddingClasses = {
    sm: "px-3 py-1",
    md: "px-4 py-1.5",
    lg: "px-5 py-2"
  }

  return (
    <div
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center rounded-lg gap-1 bg-muted text-muted-foreground",
        sizeClasses[size],
        paddingClasses[size],
        className
      )}
      role="tablist"
    >
      {options.map((option) => (
        <button
          key={option.value}
          type="button"
          role="tab"
          aria-selected={value === option.value}
          onClick={() => onValueChange(option.value)}
          className={cn(
            "!flex-1 w-0 inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
            itemPaddingClasses[size],
            value === option.value
              ? "bg-primary text-white shadow-sm"
              : "hover:bg-background/50 hover:text-foreground"
          )}
        >
          {option.icon && (
            <span className="mr-2 flex items-center">
              {option.icon}
            </span>
          )}
          {option.label}
        </button>
      ))}
    </div>
  )
})

SegmentedControl.displayName = "SegmentedControl"

export { SegmentedControl, type SegmentedControlOption }
