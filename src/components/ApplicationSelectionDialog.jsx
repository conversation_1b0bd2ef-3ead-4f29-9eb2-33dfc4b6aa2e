import React, { useState, useEffect } from 'react';
import {
  Modal,
  Row,
  Col,
  Card,
  Input,
  Button,
  Select,
  Space,
  Typography,
  Spin,
  Empty,
  Tag,
  Pagination,
} from 'antd';
import { Package } from 'lucide-react';
import { appTemplateService } from '@/services/appTemplate';

const { Title, Text } = Typography;

const ApplicationSelectionDialog = ({ visible, onClose, onSelect }) => {
  const [appTemplates, setAppTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    pageIndex: 1,
    pageSize: 12,
    keyword: '',
    appType: '',
  });
  const [total, setTotal] = useState(0);

  // 应用类型选项
  const appTypeOptions = [
    { value: '', label: '全部类型' },
    { value: 1, label: '开发工具' },
    { value: 2, label: '大模型' },
    { value: 3, label: '开源应用' },
    { value: 4, label: '帆一产品' },
    { value: 5, label: '基础镜像' },
  ];

  // 加载应用模板列表
  const loadAppTemplates = async () => {
    setLoading(true);
    try {
      const data = await appTemplateService.getAppTemplates({
        ...filters,
        status: 1, // 只获取启用的应用
      });

      setAppTemplates(data.records || []);
      setTotal(data.total || 0);
    } catch (error) {
      console.error('加载应用模板失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      pageIndex: 1,
    }));
  };

  // 处理分页变化
  const handlePageChange = (page, pageSize) => {
    setFilters(prev => ({
      ...prev,
      pageIndex: page,
      pageSize,
    }));
  };

  useEffect(() => {
    if (visible) {
      loadAppTemplates();
    }
  }, [visible, filters]);

  return (
    <Modal
      title="选择应用场景"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
    >
      <div className="space-y-4">
        {/* 筛选器 */}
        <div className="flex gap-4 items-center">
          <Input.Search
            placeholder="搜索应用名称..."
            value={filters.keyword}
            onChange={e => handleFilterChange('keyword', e.target.value)}
            onSearch={() => loadAppTemplates()}
            style={{ width: 300 }}
            allowClear
          />
          <Select
            placeholder="应用类型"
            value={filters.appType}
            onChange={value => handleFilterChange('appType', value)}
            style={{ width: 150 }}
            options={appTypeOptions}
          />
          <Button onClick={() => {
            setFilters({
              pageIndex: 1,
              pageSize: 12,
              keyword: '',
              appType: '',
            });
          }}>
            重置
          </Button>
        </div>

        {/* 应用列表 */}
        <Spin spinning={loading}>
          {appTemplates.length === 0 ? (
            <Empty description="暂无应用" />
          ) : (
            <>
              <Row gutter={[16, 16]}>
                {appTemplates.map(app => (
                  <Col xs={12} sm={8} md={6} key={app.id}>
                    <Card
                      hoverable
                      className="text-center cursor-pointer h-full"
                      onClick={() => onSelect(app.id)}
                    >
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        {app.iconUrl ? (
                          <img
                            src={app.iconUrl}
                            alt={app.name}
                            className="w-8 h-8 rounded"
                          />
                        ) : (
                          <Package className="w-6 h-6 text-blue-600" />
                        )}
                      </div>
                      <Title level={5} ellipsis={{ tooltip: app.name }} className="!mb-1">
                        {app.name}
                      </Title>
                      <Text type="secondary" className="text-xs">
                        v{app.version}
                      </Text>
                      
                      {/* 应用描述 */}
                      {app.description && (
                        <div className="mt-2">
                          <Text type="secondary" className="text-xs" title={app.description}>
                            {app.description.length > 40 ? `${app.description.slice(0, 40)}...` : app.description}
                          </Text>
                        </div>
                      )}

                      {/* 应用标签 */}
                      {app.tags && (
                        <div className="mt-2">
                          <div className="flex flex-wrap gap-1 justify-center">
                            {(typeof app.tags === 'string'
                              ? app.tags.split(',')
                              : app.tags || []
                            ).slice(0, 2).map((tag, index) => (
                              <Tag
                                bordered={false}
                                key={index}
                                color="cyan"
                                size="small"
                              >
                                {typeof tag === 'string' ? tag.trim() : tag}
                              </Tag>
                            ))}
                          </div>
                        </div>
                      )}
                    </Card>
                  </Col>
                ))}
              </Row>

              {/* 分页 */}
              <div className="flex justify-center mt-6">
                <Pagination
                  current={filters.pageIndex}
                  pageSize={filters.pageSize}
                  total={total}
                  onChange={handlePageChange}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                />
              </div>
            </>
          )}
        </Spin>
      </div>
    </Modal>
  );
};

export default ApplicationSelectionDialog;