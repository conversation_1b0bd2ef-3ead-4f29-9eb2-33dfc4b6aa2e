import { Slider, InputNumber } from 'antd';

export default function DiskInput ({ value, onChange }) {
  return <div>
    <div className="w-full flex items-center space-x-4">
      <Slider
        className='flex-1'
        min={0}
        max={1024}
        step={8}
        value={value || 0}
        onChange={onChange}
        marks={{
          0: '0GB',
          256: '256GB',
          512: '512GB',
          1024: '1TB'
        }}
      />
      <InputNumber
        min={0}
        step={8}
        value={value}
        addonAfter="GB"
        className="w-32"
        onChange={onChange}
      />
    </div>
    <div className="text-sm text-gray-500">
      💡 提示：磁盘以8GB为步进单位
    </div>
  </div>
}
