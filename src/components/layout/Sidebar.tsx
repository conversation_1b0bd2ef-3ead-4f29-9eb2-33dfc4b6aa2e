import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  ShoppingCart,
  Server,
  Store,
  Settings,
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const { user } = useAuth();

  const navItems = [
    {
      name: '控制面板',
      href: '/',
      icon: LayoutDashboard,
    },
    {
      name: '算力市场',
      href: '/compute-market',
      icon: ShoppingCart,
    },
    {
      name: '我的实例',
      href: '/my-instances',
      icon: Server,
    },
    {
      name: '应用市场',
      href: '/app-market',
      icon: Store,
    },
  ];

  // 如果是管理员，添加管理员菜单
  if (user?.role === 'admin') {
    navItems.push({
      name: '管理后台',
      href: '/admin',
      icon: Settings,
    });
  }

  return (
    <aside className="w-64 bg-white shadow-sm border-gray-200 flex-shrink-0 flex flex-col">
      {/* 导航菜单 */}
      <nav className="p-4 space-y-2 flex-1 overflow-hidden">
        {navItems.map((item) => (
          <NavLink
            key={item.href}
            to={item.href}
            className={({ isActive }) =>
              cn(
                'flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors relative group',
                isActive
                  ? 'bg-primary/10 text-primary'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )
            }
          >
            <item.icon className="w-5 h-5 flex-shrink-0" />
            <span>{item.name}</span>
          </NavLink>
        ))}
      </nav>
    </aside>
  );
};

export default Sidebar;
