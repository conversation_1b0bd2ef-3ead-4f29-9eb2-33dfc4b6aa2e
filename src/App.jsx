import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Layout from '@/components/layout/Layout';
import SuperAdminLayout from '@/components/layout/SuperAdminLayout';
import DeviceHostingLayout from '@/components/layout/DeviceHostingLayout';
import Dashboard from '@/pages/Dashboard.jsx';
import ComputeMarket from '@/pages/ComputeMarket';
import MyDevices from '@/pages/device-hosting/MyDevices';
import AppMarket from '@/pages/AppMarket';
import Profile from '@/pages/Profile';
import AdminPanel from '@/pages/AdminPanel';
import Login from '@/pages/Login';
import Register from '@/pages/Register';
// 超管页面
import SuperAdminDashboard from '@/pages/super-admin/SuperAdminDashboard';
import DeviceManagement from '@/pages/super-admin/DeviceManagement';
import AppTemplateManagement from '@/pages/super-admin/AppTemplateManagement.jsx';
import SuperAdminBillingManagement from '@/pages/super-admin/BillingManagement';
import ProfitSharingManagement from '@/pages/super-admin/ProfitSharingManagement';
// 托管设备页面
import DeviceHostingDashboard from '@/pages/device-hosting/DeviceHostingDashboard.jsx';
import OrderManagement from '@/pages/device-hosting/OrderManagement';
import MyInstances from '@/pages/MyInstances.jsx';

import { AuthProvider } from '@/contexts/AuthContext';
import { InstanceProvider } from '@/contexts/InstanceContext';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
// for date-picker i18n
import 'dayjs/locale/zh-cn';

const queryClient = new QueryClient();

const App = () => {
  return (
    <ConfigProvider
      locale={zhCN} // 设置中文语言包
      theme={{
        token: {
          // Seed Token，影响范围大
          colorPrimary: '#0d22ef',
          borderRadius: 6,
        },
      }}
    >
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="compute-matrix-theme">
          <AuthProvider>
            <InstanceProvider>
              <Router>
                <div className="min-h-screen bg-gray-50">
                  <Routes>
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />

                    {/* 超管后台路由 */}
                    <Route
                      path="/super-admin/*"
                      element={
                        <SuperAdminLayout>
                          <Routes>
                            <Route path="/" element={<SuperAdminDashboard />} />
                            <Route
                              path="/devices"
                              element={<DeviceManagement />}
                            />
                            <Route
                              path="/app-templates"
                              element={<AppTemplateManagement />}
                            />
                            <Route
                              path="/billing"
                              element={<SuperAdminBillingManagement />}
                            />
                            <Route
                              path="/profit-sharing"
                              element={<ProfitSharingManagement />}
                            />
                          </Routes>
                        </SuperAdminLayout>
                      }
                    />

                    {/* 托管设备路由 */}
                    <Route
                      path="/device-hosting/*"
                      element={
                        <DeviceHostingLayout>
                          <Routes>
                            <Route
                              path="/"
                              element={<DeviceHostingDashboard />}
                            />
                            <Route path="/my-devices" element={<MyDevices />} />
                            <Route
                              path="/orders"
                              element={<OrderManagement />}
                            />
                          </Routes>
                        </DeviceHostingLayout>
                      }
                    />

                    {/* 主应用路由 */}
                    <Route
                      path="/*"
                      element={
                        <Layout>
                          <Routes>
                            <Route path="/" element={<Dashboard />} />
                            <Route
                              path="/compute-market"
                              element={<ComputeMarket />}
                            />
                            <Route
                              path="/my-instances"
                              element={<MyInstances />}
                            />
                            <Route path="/app-market" element={<AppMarket />} />
                            <Route path="/profile" element={<Profile />} />
                            <Route path="/admin" element={<AdminPanel />} />
                          </Routes>
                        </Layout>
                      }
                    />
                  </Routes>
                </div>
                <Toaster />
              </Router>
            </InstanceProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ConfigProvider>
  );
};

export default App;
