/**
 * 用户认证服务
 */
import { mockRequest, ApiResponse } from '@/utils/api';
import { API_DELAY } from '@/utils/constants';

// 用户接口
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin' | 'super_admin';
  avatar?: string;
  createdAt: string;
}

// 登录请求参数
export interface LoginParams {
  email: string;
  password: string;
}

// 注册请求参数
export interface RegisterParams {
  username: string;
  email: string;
  password: string;
}

// 登录
export const login = async (params: LoginParams): Promise<ApiResponse<User>> => {
  const { email, password } = params;
  
  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));
  
  // 简单的角色判断逻辑
  let role: 'user' | 'admin' | 'super_admin' = 'user';
  if (email.includes('admin')) {
    role = 'admin';
  }
  if (email.includes('super')) {
    role = 'super_admin';
  }
  
  const user: User = {
    id: '1',
    username: email.split('@')[0],
    email,
    role,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
    createdAt: new Date().toISOString(),
  };
  
  return {
    success: true,
    data: user,
    message: '登录成功',
    code: 200,
  };
};

// 注册
export const register = async (params: RegisterParams): Promise<ApiResponse<User>> => {
  const { username, email, password } = params;
  
  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));
  
  const user: User = {
    id: Date.now().toString(),
    username,
    email,
    role: 'user',
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
    createdAt: new Date().toISOString(),
  };
  
  return {
    success: true,
    data: user,
    message: '注册成功',
    code: 200,
  };
};

// 获取当前用户信息
export const getCurrentUser = async (): Promise<ApiResponse<User | null>> => {
  // 从本地存储获取用户信息
  const storedUser = localStorage.getItem('user');
  
  if (!storedUser) {
    return {
      success: false,
      data: null,
      message: '未登录',
      code: 401,
    };
  }
  
  return {
    success: true,
    data: JSON.parse(storedUser),
    message: '获取成功',
    code: 200,
  };
};

// 退出登录
export const logout = async (): Promise<ApiResponse<null>> => {
  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.FAST));
  
  return {
    success: true,
    data: null,
    message: '退出成功',
    code: 200,
  };
};
