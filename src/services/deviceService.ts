/**
 * 设备管理服务
 */
import { Device, mockDevices } from '@/data/mockData';
import { mockRequest, createPaginatedResponse, ApiResponse, PaginatedResponse } from '@/utils/api';
import { API_DELAY } from '@/utils/constants';

// 获取设备列表
export const getDevices = async (params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  type?: string;
  location?: string;
}): Promise<PaginatedResponse<Device>> => {
  const { page = 1, pageSize = 10, status, type, location } = params || {};
  
  let filteredDevices = [...mockDevices];
  
  // 过滤条件
  if (status) {
    filteredDevices = filteredDevices.filter(device => device.status === status);
  }
  if (type) {
    filteredDevices = filteredDevices.filter(device => device.type === type);
  }
  if (location) {
    filteredDevices = filteredDevices.filter(device => device.location === location);
  }
  
  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));
  
  return createPaginatedResponse(filteredDevices, page, pageSize);
};

// 获取单个设备详情
export const getDevice = async (id: string): Promise<ApiResponse<Device>> => {
  const device = mockDevices.find(d => d.id === id);
  
  if (!device) {
    throw new Error('设备不存在');
  }
  
  return mockRequest(device, { delay: API_DELAY.FAST });
};

// 更新设备状态
export const updateDeviceStatus = async (
  id: string, 
  status: 'online' | 'offline' | 'maintenance'
): Promise<ApiResponse<Device>> => {
  const deviceIndex = mockDevices.findIndex(d => d.id === id);
  
  if (deviceIndex === -1) {
    throw new Error('设备不存在');
  }
  
  // 更新设备状态
  mockDevices[deviceIndex] = {
    ...mockDevices[deviceIndex],
    status,
  };
  
  return mockRequest(mockDevices[deviceIndex], { delay: API_DELAY.NORMAL });
};

// 强制下架设备
export const forceOfflineDevice = async (id: string): Promise<ApiResponse<Device>> => {
  return updateDeviceStatus(id, 'offline');
};

// 分配租户
export const assignTenant = async (
  deviceId: string, 
  tenantId: string
): Promise<ApiResponse<{ deviceId: string; tenantId: string }>> => {
  const device = mockDevices.find(d => d.id === deviceId);
  
  if (!device) {
    throw new Error('设备不存在');
  }
  
  return mockRequest(
    { deviceId, tenantId },
    { delay: API_DELAY.NORMAL }
  );
};

// 获取设备统计信息
export const getDeviceStats = async (): Promise<ApiResponse<{
  total: number;
  online: number;
  offline: number;
  maintenance: number;
  totalRevenue: number;
  avgUtilization: number;
}>> => {
  const stats = {
    total: mockDevices.length,
    online: mockDevices.filter(d => d.status === 'online').length,
    offline: mockDevices.filter(d => d.status === 'offline').length,
    maintenance: mockDevices.filter(d => d.status === 'maintenance').length,
    totalRevenue: mockDevices.reduce((sum, d) => sum + (d.pricePerHour * d.totalOrders * 2), 0),
    avgUtilization: 0.75, // 模拟平均利用率
  };
  
  return mockRequest(stats, { delay: API_DELAY.FAST });
};
