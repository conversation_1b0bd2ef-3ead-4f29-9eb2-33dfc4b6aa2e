import http from '@/utils/http';

const API_BASE = '/ihmp';

/**
 * 设备管理服务
 */
export const deviceService = {
  /**
   * 获取已验证成功设备分页列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 当前页码
   * @param {number} params.pageSize 每页大小
   * @param {string} params.machineName 设备名称
   * @param {number} params.healthStatus 健康状态 (1:健康, 0:异常)
   * @param {string} params.machineType 设备类型 (GPU/CPU/STORAGE)
   * @param {number} params.isPublic 是否上架 (1:已上架, 0:未上架)
   */
  getMachines: (params = {}) => {
    // 过滤掉空值参数
    const filteredParams = Object.keys(params).reduce((acc, key) => {
      if (params[key] !== undefined && params[key] !== '') {
        acc[key] = params[key];
      }
      return acc;
    }, {});
    return http.post(`${API_BASE}/machine/page`, filteredParams);
  },

  /**
   * 获取设备接入记录分页列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 当前页码
   * @param {number} params.pageSize 每页大小
   */
  getAccessRecords: (params = {}) => {
    return http.post(`${API_BASE}/api/v1/record/page`, params);
  },

  /**
   * 获取总览统计数据
   */
  getOverviewCount: () => {
    return http.get(`${API_BASE}/overview/count`);
  },

  /**
   * 设备SSH接入
   * @param {Object} data SSH接入参数
   * @param {string} data.host 主机地址
   * @param {string} data.port 端口号
   * @param {string} data.userName 用户名
   * @param {string} data.passWord 密码
   * @param {string} data.machineName 设备名称
   */
  sshConnect: (data) => {
    return http.post(`${API_BASE}/api/v1/machines/ssh`, data);
  },

  /**
   * 手动接入生成命令
   * @param {Object} data 生成命令参数
   * @param {string} data.machineName 设备名称
   */
  generateCommand: (data) => {
    return http.post(`${API_BASE}/api/v1/command`, data);
  },

  /**
   * 处理安装结果
   * @param {Object} data 安装结果数据
   * @param {string} data.status 状态 (SUCCESS/FAILURE)
   * @param {string} data.stage 阶段
   * @param {string} data.message 消息
   * @param {string} data.details 详细信息
   * @param {string} data.token 令牌
   */
  processInstallResult: (data) => {
    return http.post(`${API_BASE}/api/v1/machines/access`, data);
  }
};

/**
 * 设备状态枚举
 */
export const DEVICE_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  CONNECTING: 'connecting',
  ERROR: 'error'
};

/**
 * 设备状态标签映射
 */
export const DEVICE_STATUS_LABELS = {
  [DEVICE_STATUS.ONLINE]: '在线',
  [DEVICE_STATUS.OFFLINE]: '离线',
  [DEVICE_STATUS.CONNECTING]: '连接中',
  [DEVICE_STATUS.ERROR]: '错误'
};

/**
 * 接入方式枚举
 */
export const ACCESS_METHODS = {
  SSH: 'ssh',
  MANUAL: 'manual'
};

/**
 * 接入方式标签映射
 */
export const ACCESS_METHOD_LABELS = {
  [ACCESS_METHODS.SSH]: 'SSH自动接入',
  [ACCESS_METHODS.MANUAL]: '手动接入'
};