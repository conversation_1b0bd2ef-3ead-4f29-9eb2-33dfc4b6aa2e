/**
 * 分润管理服务
 */
import {
  mockRequest,
  createPaginatedResponse,
  ApiResponse,
  PaginatedResponse,
} from '@/utils/api';
import { API_DELAY } from '@/utils/constants';

// 分润规则接口
export interface ProfitSharingRule {
  id: string;
  name: string;
  type: 'global' | 'team' | 'instance';
  targetId?: string; // 团队ID或实例ID
  platformRate: number; // 平台抽成比例
  providerRate: number; // 提供方分成比例
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

// 模拟分润规则数据
const mockProfitSharingRules: ProfitSharingRule[] = [
  {
    id: 'rule-1',
    name: '全局默认分润规则',
    type: 'global',
    platformRate: 0.15, // 平台抽成15%
    providerRate: 0.85, // 提供方分成85%
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
  },
  {
    id: 'rule-2',
    name: '高级团队分润规则',
    type: 'team',
    targetId: 'team-1',
    platformRate: 0.1, // 平台抽成10%
    providerRate: 0.9, // 提供方分成90%
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-05T00:00:00Z',
    isActive: true,
  },
  {
    id: 'rule-4',
    name: '高性能实例分润规则',
    type: 'instance',
    targetId: 'inst-1',
    platformRate: 0.12, // 平台抽成12%
    providerRate: 0.88, // 提供方分成88%
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    isActive: true,
  },
];

// 获取分润规则列表
export const getProfitSharingRules = async (params?: {
  page?: number;
  pageSize?: number;
  type?: string;
  isActive?: boolean;
}): Promise<PaginatedResponse<ProfitSharingRule>> => {
  const { page = 1, pageSize = 10, type, isActive } = params || {};

  let filteredRules = [...mockProfitSharingRules];

  if (type) {
    filteredRules = filteredRules.filter(rule => rule.type === type);
  }
  if (isActive !== undefined) {
    filteredRules = filteredRules.filter(rule => rule.isActive === isActive);
  }

  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));

  return createPaginatedResponse(filteredRules, page, pageSize);
};

// 获取单个分润规则
export const getProfitSharingRule = async (
  id: string
): Promise<ApiResponse<ProfitSharingRule>> => {
  const rule = mockProfitSharingRules.find(r => r.id === id);

  if (!rule) {
    throw new Error('分润规则不存在');
  }

  return mockRequest(rule, { delay: API_DELAY.FAST });
};

// 创建分润规则
export const createProfitSharingRule = async (
  ruleData: Omit<ProfitSharingRule, 'id' | 'createdAt' | 'updatedAt'>
): Promise<ApiResponse<ProfitSharingRule>> => {
  const now = new Date().toISOString();

  const newRule: ProfitSharingRule = {
    ...ruleData,
    id: `rule-${Date.now()}`,
    createdAt: now,
    updatedAt: now,
  };

  mockProfitSharingRules.push(newRule);

  return mockRequest(newRule, { delay: API_DELAY.NORMAL });
};

// 更新分润规则
export const updateProfitSharingRule = async (
  id: string,
  updates: Partial<ProfitSharingRule>
): Promise<ApiResponse<ProfitSharingRule>> => {
  const ruleIndex = mockProfitSharingRules.findIndex(r => r.id === id);

  if (ruleIndex === -1) {
    throw new Error('分润规则不存在');
  }

  mockProfitSharingRules[ruleIndex] = {
    ...mockProfitSharingRules[ruleIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  return mockRequest(mockProfitSharingRules[ruleIndex], {
    delay: API_DELAY.NORMAL,
  });
};

// 删除分润规则
export const deleteProfitSharingRule = async (
  id: string
): Promise<ApiResponse<{ id: string }>> => {
  const ruleIndex = mockProfitSharingRules.findIndex(r => r.id === id);

  if (ruleIndex === -1) {
    throw new Error('分润规则不存在');
  }

  mockProfitSharingRules.splice(ruleIndex, 1);

  return mockRequest({ id }, { delay: API_DELAY.NORMAL });
};

// 获取分润统计信息
export const getProfitSharingStats = async (): Promise<
  ApiResponse<{
    totalPlatformRevenue: number;
    totalProviderRevenue: number;
    avgPlatformRate: number;
  }>
> => {
  const stats = {
    totalPlatformRevenue: 15000,
    totalProviderRevenue: 75000,
    avgPlatformRate: 0.15,
  };

  return mockRequest(stats, { delay: API_DELAY.FAST });
};
