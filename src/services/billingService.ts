/**
 * 账单管理服务
 */
import { mockRequest, createPaginatedResponse, ApiResponse, PaginatedResponse } from '@/utils/api';
import { API_DELAY } from '@/utils/constants';

// 账单记录接口
export interface BillingRecord {
  id: string;
  userId: string;
  instanceId: string;
  instanceName: string;
  type: 'instance' | 'application' | 'storage';
  amount: number;
  startTime: string;
  endTime: string;
  duration: number; // 小时
  status: 'paid' | 'pending' | 'failed';
  createdAt: string;
}

// 收益记录接口
export interface RevenueRecord {
  id: string;
  deviceId: string;
  deviceName: string;
  userId: string;
  instanceId: string;
  amount: number;
  commission: number; // 平台抽成
  netRevenue: number; // 净收益
  startTime: string;
  endTime: string;
  duration: number;
  status: 'settled' | 'pending';
  createdAt: string;
}

// 模拟账单数据
const mockBillingRecords: BillingRecord[] = [
  {
    id: 'bill-1',
    userId: '1',
    instanceId: 'inst-1',
    instanceName: 'DeepSeek训练实例',
    type: 'instance',
    amount: 130.8,
    startTime: '2024-01-18T08:00:00Z',
    endTime: '2024-01-20T08:00:00Z',
    duration: 48,
    status: 'paid',
    createdAt: '2024-01-20T08:00:00Z',
  },
  {
    id: 'bill-2',
    userId: '1',
    instanceId: 'inst-2',
    instanceName: 'Stable Diffusion实例',
    type: 'instance',
    amount: 204.0,
    startTime: '2024-01-17T14:00:00Z',
    endTime: '2024-01-19T14:00:00Z',
    duration: 48,
    status: 'paid',
    createdAt: '2024-01-19T14:00:00Z',
  },
  {
    id: 'bill-3',
    userId: '1',
    instanceId: 'inst-1',
    instanceName: 'DeepSeek Coder应用',
    type: 'application',
    amount: 57.6,
    startTime: '2024-01-18T08:00:00Z',
    endTime: '2024-01-20T08:00:00Z',
    duration: 48,
    status: 'paid',
    createdAt: '2024-01-20T08:00:00Z',
  },
];

// 模拟收益数据
const mockRevenueRecords: RevenueRecord[] = [
  {
    id: 'rev-1',
    deviceId: '1',
    deviceName: 'AI训练服务器-01',
    userId: '1',
    instanceId: 'inst-1',
    amount: 130.8,
    commission: 19.62, // 15%抽成
    netRevenue: 111.18,
    startTime: '2024-01-18T08:00:00Z',
    endTime: '2024-01-20T08:00:00Z',
    duration: 48,
    status: 'settled',
    createdAt: '2024-01-20T08:00:00Z',
  },
  {
    id: 'rev-2',
    deviceId: '2',
    deviceName: '渲染节点-04',
    userId: '1',
    instanceId: 'inst-2',
    amount: 204.0,
    commission: 30.6,
    netRevenue: 173.4,
    startTime: '2024-01-17T14:00:00Z',
    endTime: '2024-01-19T14:00:00Z',
    duration: 48,
    status: 'settled',
    createdAt: '2024-01-19T14:00:00Z',
  },
];

// 获取用户账单记录
export const getUserBillingRecords = async (params?: {
  userId: string;
  page?: number;
  pageSize?: number;
  type?: string;
  status?: string;
}): Promise<PaginatedResponse<BillingRecord>> => {
  const { userId, page = 1, pageSize = 10, type, status } = params || {};
  
  let filteredRecords = mockBillingRecords.filter(record => record.userId === userId);
  
  if (type) {
    filteredRecords = filteredRecords.filter(record => record.type === type);
  }
  if (status) {
    filteredRecords = filteredRecords.filter(record => record.status === status);
  }
  
  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));
  
  return createPaginatedResponse(filteredRecords, page, pageSize);
};

// 获取用户收益记录
export const getUserRevenueRecords = async (params?: {
  userId: string;
  page?: number;
  pageSize?: number;
  status?: string;
}): Promise<PaginatedResponse<RevenueRecord>> => {
  const { userId, page = 1, pageSize = 10, status } = params || {};
  
  let filteredRecords = mockRevenueRecords.filter(record => record.userId === userId);
  
  if (status) {
    filteredRecords = filteredRecords.filter(record => record.status === status);
  }
  
  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));
  
  return createPaginatedResponse(filteredRecords, page, pageSize);
};

// 获取账单统计
export const getBillingStats = async (userId: string): Promise<ApiResponse<{
  totalSpent: number;
  thisMonthSpent: number;
  totalRevenue: number;
  thisMonthRevenue: number;
  pendingAmount: number;
}>> => {
  const userBills = mockBillingRecords.filter(record => record.userId === userId);
  const userRevenues = mockRevenueRecords.filter(record => record.userId === userId);
  
  const totalSpent = userBills.reduce((sum, bill) => sum + bill.amount, 0);
  const totalRevenue = userRevenues.reduce((sum, rev) => sum + rev.netRevenue, 0);
  
  // 模拟本月数据（简化计算）
  const thisMonthSpent = totalSpent * 0.3;
  const thisMonthRevenue = totalRevenue * 0.4;
  const pendingAmount = userBills
    .filter(bill => bill.status === 'pending')
    .reduce((sum, bill) => sum + bill.amount, 0);
  
  const stats = {
    totalSpent,
    thisMonthSpent,
    totalRevenue,
    thisMonthRevenue,
    pendingAmount,
  };
  
  return mockRequest(stats, { delay: API_DELAY.FAST });
};
