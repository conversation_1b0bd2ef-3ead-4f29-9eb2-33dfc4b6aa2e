import http from '@/utils/http';

/**
 * 获取算力市场分页列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageIndex - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {number} [params.cpuNum] - 硬件要求：最低CPU核数
 * @param {number} [params.memorySize] - 硬件要求：最低内存大小
 * @param {string} [params.minGpuCode] - 硬件要求：最低GPU型号（CPU时不填写）
 * @param {string} [params.machineName] - 机器名称模糊搜索
 * @param {string} [params.machineType] - 设备类型
 * @param {number} [params.regionCode] - 区域id
 * @param {string} [params.code] - CPU/GPU型号
 * @returns {Promise} 算力市场列表数据
 */
export const getMarketList = (params) => {
  return http.post('/ihmp/market/page', params);
};
