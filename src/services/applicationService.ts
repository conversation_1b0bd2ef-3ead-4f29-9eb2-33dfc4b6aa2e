/**
 * 应用管理服务
 */
import { Application, mockApplications } from '@/data/mockData';
import {
  mockRequest,
  createPaginatedResponse,
  ApiResponse,
  PaginatedResponse,
} from '@/utils/api';
import { API_DELAY } from '@/utils/constants';

// 获取应用列表
export const getApplications = async (params?: {
  page?: number;
  pageSize?: number;
  category?: string;
  isOfficial?: boolean;
}): Promise<PaginatedResponse<Application>> => {
  const { page = 1, pageSize = 10, category, isOfficial } = params || {};

  let filteredApps = [...mockApplications];

  // 过滤条件
  if (category) {
    filteredApps = filteredApps.filter(app => app.category === category);
  }
  if (isOfficial !== undefined) {
    filteredApps = filteredApps.filter(app => app.isOfficial === isOfficial);
  }

  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));

  return createPaginatedResponse(filteredApps, page, pageSize);
};

// 获取单个应用详情
export const getApplication = async (
  id: string
): Promise<ApiResponse<Application>> => {
  const app = mockApplications.find(a => a.id === id);

  if (!app) {
    throw new Error('应用不存在');
  }

  return mockRequest(app, { delay: API_DELAY.FAST });
};

// 创建应用模板
export const createApplication = async (
  appData: Omit<Application, 'id' | 'downloads' | 'createdAt'>
): Promise<ApiResponse<Application>> => {
  const newApp: Application = {
    ...appData,
    id: `app-${Date.now()}`,
    downloads: 0,
    createdAt: new Date().toISOString(),
  };

  mockApplications.push(newApp);

  return mockRequest(newApp, { delay: API_DELAY.SLOW });
};

// 更新应用模板
export const updateApplication = async (
  id: string,
  updates: Partial<Application>
): Promise<ApiResponse<Application>> => {
  const appIndex = mockApplications.findIndex(a => a.id === id);

  if (appIndex === -1) {
    throw new Error('应用不存在');
  }

  mockApplications[appIndex] = {
    ...mockApplications[appIndex],
    ...updates,
  };

  return mockRequest(mockApplications[appIndex], { delay: API_DELAY.NORMAL });
};

// 删除应用模板
export const deleteApplication = async (
  id: string
): Promise<ApiResponse<{ id: string }>> => {
  const appIndex = mockApplications.findIndex(a => a.id === id);

  if (appIndex === -1) {
    throw new Error('应用不存在');
  }

  mockApplications.splice(appIndex, 1);

  return mockRequest({ id }, { delay: API_DELAY.NORMAL });
};

// 上架/下架应用
export const toggleApplicationStatus = async (
  id: string,
  isActive: boolean
): Promise<ApiResponse<Application>> => {
  const appIndex = mockApplications.findIndex(a => a.id === id);

  if (appIndex === -1) {
    throw new Error('应用不存在');
  }

  // 这里可以添加一个 isActive 字段到 Application 接口
  // 暂时使用 description 来模拟状态
  const currentDesc = mockApplications[appIndex].description;
  mockApplications[appIndex] = {
    ...mockApplications[appIndex],
    description: isActive
      ? currentDesc.replace('[已下架] ', '')
      : currentDesc.startsWith('[已下架]')
      ? currentDesc
      : `[已下架] ${currentDesc}`,
  };

  return mockRequest(mockApplications[appIndex], { delay: API_DELAY.NORMAL });
};

// 获取应用分类
export const getApplicationCategories = async (): Promise<
  ApiResponse<string[]>
> => {
  const categories = [...new Set(mockApplications.map(app => app.category))];

  return mockRequest(categories, { delay: API_DELAY.FAST });
};
