import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount)
}

export function formatDate(date: Date | string): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}


export function getOssUrl(url):string{
  return `/oss/${url}`
}

// 生成名称的通用函数

export function generateName(prefix: string='', includeTimestamp = true) {
  console.log(prefix);
  
  const cleanName = prefix?`${prefix.toLowerCase().replace(/[^a-z0-9]/g, '-')}-`:'';

  const randomStr = Math.random().toString(36).substring(2,9);
  const timestamp = Date.now().toString().slice(-4);
  return `${cleanName}${randomStr}-${timestamp}`;
}
