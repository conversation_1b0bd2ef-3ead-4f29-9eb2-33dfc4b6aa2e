import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { Instance } from '@/data/mockData';
import { getInstances, createInstance, updateInstanceStatus, deleteInstance as deleteInstanceService } from '@/services/instanceService';

interface InstanceContextType {
  instances: Instance[];
  loading: boolean;
  addInstance: (instance: Omit<Instance, 'id'>) => Promise<Instance>;
  updateInstance: (id: string, updates: Partial<Instance>) => Promise<void>;
  deleteInstance: (id: string) => Promise<void>;
  refreshInstances: () => Promise<void>;
}

const InstanceContext = createContext<InstanceContextType | undefined>(undefined);

export const useInstances = () => {
  const context = useContext(InstanceContext);
  if (!context) {
    throw new Error('useInstances must be used within an InstanceProvider');
  }
  return context;
};

interface InstanceProviderProps {
  children: ReactNode;
}

export const InstanceProvider: React.FC<InstanceProviderProps> = ({ children }) => {
  const [instances, setInstances] = useState<Instance[]>([]);
  const [loading, setLoading] = useState(false);

  const refreshInstances = async () => {
    setLoading(true);
    try {
      const response = await getInstances();
      if (response.success) {
        setInstances(response.data.items);
      }
    } catch (error) {
      console.error('获取实例列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const addInstance = async (instanceData: Omit<Instance, 'id'>): Promise<Instance> => {
    try {
      const response = await createInstance(instanceData);
      if (response.success && response.data) {
        setInstances(prev => [...prev, response.data!]);
        return response.data;
      }
      throw new Error('创建实例失败');
    } catch (error) {
      console.error('创建实例失败:', error);
      throw error;
    }
  };

  const updateInstance = async (id: string, updates: Partial<Instance>) => {
    try {
      if (updates.status) {
        const response = await updateInstanceStatus(id, updates.status);
        if (response.success && response.data) {
          setInstances(prev => prev.map(instance =>
            instance.id === id ? response.data! : instance
          ));
        }
      }
    } catch (error) {
      console.error('更新实例失败:', error);
      throw error;
    }
  };

  const deleteInstance = async (id: string) => {
    try {
      await deleteInstanceService(id);
      setInstances(prev => prev.filter(instance => instance.id !== id));
    } catch (error) {
      console.error('删除实例失败:', error);
      throw error;
    }
  };

  // 初始化时加载实例
  useEffect(() => {
    refreshInstances();
  }, []);

  const value = {
    instances,
    loading,
    addInstance,
    updateInstance,
    deleteInstance,
    refreshInstances,
  };

  return (
    <InstanceContext.Provider value={value}>
      {children}
    </InstanceContext.Provider>
  );
};
