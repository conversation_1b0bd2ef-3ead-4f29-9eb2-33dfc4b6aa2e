import React, { useEffect, useState } from 'react';
import {
  Table,
  Card,
  Tag,
  Select,
  DatePicker,
  Space,
  Statistic,
  Row,
  Col,
  Input,
  Button,
} from 'antd';
import {
  DollarSign,
  TrendingUp,
  CreditCard,
  Clock,
  Search,
  Download,
} from 'lucide-react';
import { getUserBillingRecords } from '@/services/billingService';
import { formatPrice, formatTime } from '@/utils/format';

const { Option } = Select;
const { RangePicker } = DatePicker;

const BillingManagement = () => {
  const [billingRecords, setBillingRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');

  useEffect(() => {
    fetchData();
  }, [statusFilter, typeFilter]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 获取所有用户的账单记录（超管视角）
      const response = await getUserBillingRecords({
        userId: 'all', // 特殊标识获取所有用户
        status: statusFilter || undefined,
        type: typeFilter || undefined,
      });
      if (response.success) {
        setBillingRecords(response.data.items);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 模拟统计数据
  const stats = {
    totalRevenue: 125680.5,
    thisMonthRevenue: 45680.2,
    pendingAmount: 8950.0,
    totalOrders: 1248,
  };

  const columns = [
    {
      title: '用户ID',
      dataIndex: 'userId',
      key: 'userId',
      width: 100,
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) =>
        record.userId.toLowerCase().includes(value.toLowerCase()) ||
        record.instanceName.toLowerCase().includes(value.toLowerCase()),
      render: userId => <span className="font-mono text-sm">{userId}</span>,
    },
    {
      title: '实例名称',
      dataIndex: 'instanceName',
      key: 'instanceName',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: type => {
        const typeMap = {
          instance: { color: 'blue', text: '实例' },
          application: { color: 'green', text: '应用' },
          storage: { color: 'orange', text: '存储' },
        };
        const config = typeMap[type];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: amount => (
        <span className="font-medium">{formatPrice(amount)}</span>
      ),
      sorter: (a, b) => a.amount - b.amount,
    },
    {
      title: '使用时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: duration => `${duration}小时`,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 150,
      render: time => formatTime(time),
      sorter: (a, b) => new Date(a.startTime) - new Date(b.startTime),
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 150,
      render: time => formatTime(time),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: status => {
        const statusMap = {
          paid: { color: 'green', text: '已支付' },
          pending: { color: 'orange', text: '待支付' },
          failed: { color: 'red', text: '支付失败' },
        };
        const config = statusMap[status];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: time => formatTime(time),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">账单管理</h1>
          <p className="text-gray-600 mt-1">查看和管理所有用户的账单记录</p>
        </div>
        <Button icon={<Download className="w-4 h-4" />}>导出账单</Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={stats.totalRevenue}
              prefix={<DollarSign className="w-4 h-4 text-green-500" />}
              valueStyle={{ color: '#52c41a' }}
              formatter={value => formatPrice(Number(value))}
            />
            <div className="mt-2 text-sm text-gray-500">
              本月: {formatPrice(stats.thisMonthRevenue)}
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待收款"
              value={stats.pendingAmount}
              prefix={<Clock className="w-4 h-4 text-orange-500" />}
              valueStyle={{ color: '#fa8c16' }}
              formatter={value => formatPrice(Number(value))}
            />
            <div className="mt-2 text-sm text-gray-500">需要跟进</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={stats.totalOrders}
              prefix={<CreditCard className="w-4 h-4 text-blue-500" />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div className="mt-2 text-sm text-gray-500">本月: 156 订单</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均订单金额"
              value={stats.totalRevenue / stats.totalOrders}
              prefix={<TrendingUp className="w-4 h-4 text-purple-500" />}
              valueStyle={{ color: '#722ed1' }}
              formatter={value => formatPrice(Number(value))}
              precision={2}
            />
            <div className="mt-2 text-sm text-gray-500">较上月 +8.5%</div>
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card>
        <div className="flex flex-wrap gap-4 items-center">
          <Input
            placeholder="搜索用户ID或实例名称"
            prefix={<Search className="w-4 h-4 text-gray-400" />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder="账单类型"
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="instance">实例</Option>
            <Option value="application">应用</Option>
            <Option value="storage">存储</Option>
          </Select>
          <Select
            placeholder="状态筛选"
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="paid">已支付</Option>
            <Option value="pending">待支付</Option>
            <Option value="failed">支付失败</Option>
          </Select>
          <RangePicker placeholder={['开始日期', '结束日期']} />
          <Button onClick={fetchData}>刷新</Button>
        </div>
      </Card>

      {/* 账单列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={billingRecords}
          loading={loading}
          rowKey="id"
          pagination={{
            total: billingRecords.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: total => `共 ${total} 条账单记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default BillingManagement;
