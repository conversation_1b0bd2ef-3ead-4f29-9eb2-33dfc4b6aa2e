import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import {
  Search,
  RefreshCw,
  Package,
  Brain,
  Code,
  Sparkles,
  HardDrive,
} from 'lucide-react';
import {
  appTemplateService,
  APP_TYPES,
  APP_TYPE_LABELS,
} from '@/services/appTemplate';
import { useRequest } from 'ahooks';
import AppTemplateFormModal from './components/AppTemplateFormModal';
import { toast } from 'sonner';

const { Text } = Typography;
const { Option } = Select;

const AppTemplateManagement = () => {
  const [appTemplates, setAppTemplates] = useState([]);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('create');
  const [currentRecord, setCurrentRecord] = useState(null);

  // 查询参数
  const [queryParams, setQueryParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    name: '',
    appType: void 0, // undefined 表示不筛选
    status: void 0,
    isOfficial: void 0,
    keyword: '',
    sortType: 'latest',
  });

  // 使用 useRequest 管理应用模板列表加载
  const { loading, refresh } = useRequest(
    () => appTemplateService.getAppTemplates(queryParams),
    {
      refreshDeps: [queryParams],
      onSuccess: data => {
        setAppTemplates(data.records || []);
        setTotal(data.total || 0);
      },
      onError: () => {
        toast.error('加载应用模板列表失败');
      },
    }
  );

  // 处理查询参数变化
  const handleQueryChange = (key, value) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
      pageIndex: 1, // 重置到第一页
    }));
  };

  // 重置搜索条件
  const handleReset = () => {
    setQueryParams({
      pageIndex: 1,
      pageSize: 10,
      name: '',
      appType: void 0,
      status: void 0,
      isOfficial: void 0,
      keyword: '',
      sortType: 'latest',
    });
  };

  // 处理分页变化
  const handleTableChange = pagination => {
    setQueryParams(prev => ({
      ...prev,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize,
    }));
  };

  // 打开模态框
  const openModal = (type, record) => {
    setModalType(type);
    setCurrentRecord(record || null);
    setShowModal(true);
  };

  // 关闭模态框
  const closeModal = () => {
    setShowModal(false);
    setCurrentRecord(null);
  };

  // 处理成功回调
  const handleSuccess = () => {
    refresh();
  };

  // 删除应用模板 - 支持单个和批量删除
  const handleDelete = async (records = []) => {
    // 如果没有传入记录，则使用选中的记录进行批量删除
    const targetRecords =
      records.length > 0
        ? records
        : selectedRowKeys.map(key => ({ id: Number(key) }));

    if (targetRecords.length === 0) {
      toast.warning('请选择要删除的记录');
      return;
    }

    const ids = targetRecords.map(record => record.id);
    const isBatch = records.length === 0;

    try {
      await appTemplateService.deleteAppTemplates(ids);
      toast.success(`${isBatch ? '批量' : ''}删除成功`);

      if (isBatch) {
        setSelectedRowKeys([]);
      }
      refresh();
    } catch (error) {
      toast.error(`${isBatch ? '批量' : ''}删除失败`);
    }
  };

  // 切换状态 - 支持单个和批量切换
  const handleToggleStatus = async (status, records = []) => {
    // 如果没有传入记录，则使用选中的记录进行批量操作
    const targetRecords =
      records.length > 0
        ? records
        : selectedRowKeys.map(key => ({ id: Number(key) }));

    if (targetRecords.length === 0) {
      toast.warning('请选择要操作的记录');
      return;
    }

    const ids = targetRecords.map(record => record.id);
    const isBatch = records.length === 0;

    try {
      if (isBatch) {
        // 批量切换状态
        await appTemplateService.batchToggleStatus(ids, status);
      } else {
        // 单个切换状态
        await appTemplateService.toggleStatus(ids[0], status);
      }

      toast.success(
        `${isBatch ? '批量' : ''}${status === 1 ? '启用' : '禁用'}成功`
      );

      if (isBatch) {
        setSelectedRowKeys([]);
      }
      refresh();
    } catch (error) {
      toast.error(`${isBatch ? '批量' : ''}状态更新失败`);
    }
  };

  // 应用类型图标映射
  const getAppTypeIcon = appType => {
    const iconMap = {
      [APP_TYPES.DEVELOPMENT_TOOL]: <Code className="w-4 h-4" />,
      [APP_TYPES.AI_MODEL]: <Brain className="w-4 h-4" />,
      [APP_TYPES.OPEN_SOURCE]: <Package className="w-4 h-4" />,
      [APP_TYPES.FANYI_PRODUCT]: <Sparkles className="w-4 h-4" />,
      [APP_TYPES.BASIC_IMAGE]: <HardDrive className="w-4 h-4" />,
    };
    return iconMap[appType] || <Package className="w-4 h-4" />;
  };

  // 获取应用类型颜色
  const getAppTypeColor = appType => {
    const colorMap = {
      [APP_TYPES.DEVELOPMENT_TOOL]: 'blue',
      [APP_TYPES.AI_MODEL]: 'purple',
      [APP_TYPES.OPEN_SOURCE]: 'green',
      [APP_TYPES.FANYI_PRODUCT]: 'gold',
      [APP_TYPES.BASIC_IMAGE]: 'cyan',
    };
    return colorMap[appType] || 'default';
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">应用模板管理</h1>
        <p className="text-gray-600 mt-1">管理平台上的所有应用模板</p>
      </div>

      {/* 筛选器 */}
      <Card>
        <div className="flex justify-between items-center">
          <div className="flex gap-4 items-center">
            <Input
              placeholder="搜索应用名称..."
              prefix={<Search className="w-4 h-4 text-gray-400" />}
              value={queryParams.keyword}
              onChange={e => handleQueryChange('keyword', e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="应用类型"
              value={queryParams.appType}
              onChange={value => handleQueryChange('appType', value)}
              style={{ width: 120 }}
              allowClear
            >
              {Object.entries(APP_TYPE_LABELS).map(([key, label]) => (
                <Option key={key} value={parseInt(key)}>
                  {label}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="状态"
              value={queryParams.status}
              onChange={value => handleQueryChange('status', value)}
              style={{ width: 100 }}
              allowClear
            >
              <Option value={1}>启用</Option>
              <Option value={0}>禁用</Option>
            </Select>
            <Select
              placeholder="应用来源"
              value={queryParams.isOfficial}
              onChange={value => handleQueryChange('isOfficial', value)}
              style={{ width: 120 }}
              allowClear
            >
              <Option value={1}>官方应用</Option>
              <Option value={0}>社区应用</Option>
            </Select>
            <Button onClick={handleReset}>重置</Button>
            <Button
              icon={<RefreshCw className="w-4 h-4" />}
              onClick={() => refresh()}
            />
          </div>

          <div className="flex gap-2 items-center">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openModal('create')}
            >
              新增应用
            </Button>
          </div>
        </div>

        {selectedRowKeys.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Space>
              <span className="text-gray-600">
                已选择 {selectedRowKeys.length} 项
              </span>
              <Button size="small" onClick={() => handleToggleStatus(1)}>
                批量启用
              </Button>
              <Button size="small" onClick={() => handleToggleStatus(0)}>
                批量禁用
              </Button>
              <Popconfirm
                title="确定要删除选中的应用模板吗？"
                onConfirm={() => handleDelete()}
                okText="确定"
                cancelText="取消"
              >
                <Button size="small" danger>
                  批量删除
                </Button>
              </Popconfirm>
            </Space>
          </div>
        )}
      </Card>

      {/* 表格 */}
      <Card>
        <Table
          columns={[
            {
              title: '应用名称',
              dataIndex: 'name',
              width: 200,
              fixed: 'left',
              render: (text, record) => (
                <Space>
                  {record.iconUrl && (
                    <img
                      src={record.iconUrl}
                      alt="icon"
                      className="w-6 h-6 rounded"
                    />
                  )}
                  <div>
                    <div className="font-medium">{text}</div>
                    <Text type="secondary" className="text-xs">
                      v{record.version}
                    </Text>
                  </div>
                </Space>
              ),
            },
            {
              title: '应用类型',
              dataIndex: 'appType',
              width: 120,
              render: appType => (
                <Tag
                  color={getAppTypeColor(appType)}
                  icon={getAppTypeIcon(appType)}
                >
                  {APP_TYPE_LABELS[appType]}
                </Tag>
              ),
            },
            {
              title: '状态',
              dataIndex: 'status',
              width: 100,
              render: (status, record) => (
                <Switch
                  checked={status === 1}
                  onChange={checked =>
                    handleToggleStatus(checked ? 1 : 0, [record])
                  }
                  size="small"
                />
              ),
            },
            {
              title: '官方应用',
              dataIndex: 'isOfficial',
              width: 100,
              render: isOfficial => (
                <Tag color={isOfficial === 1 ? 'gold' : 'default'}>
                  {isOfficial === 1 ? '官方' : '社区'}
                </Tag>
              ),
            },
            {
              title: '资源要求',
              width: 150,
              render: (_, record) => (
                <Space direction="vertical" size="small">
                  <Text className="text-xs">CPU: {record.minCpuCores}核</Text>
                  <Text className="text-xs">内存: {record.minMemoryGb}GB</Text>
                  {record.minGpuCount > 0 && (
                    <Text className="text-xs">GPU: {record.minGpuCount}卡</Text>
                  )}
                </Space>
              ),
            },
            {
              title: '下载次数',
              dataIndex: 'downloadCount',
              width: 100,
              render: count => count || 0,
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              width: 150,
              render: time => (time ? new Date(time).toLocaleString() : '-'),
            },
            {
              title: '操作',
              width: 200,
              fixed: 'right',
              render: (_, record) => (
                <Space>
                  <Tooltip title="查看">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => openModal('view', record)}
                    />
                  </Tooltip>
                  <Tooltip title="编辑">
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => openModal('edit', record)}
                    />
                  </Tooltip>
                  <Tooltip title="复制">
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => openModal('copy', record)}
                    />
                  </Tooltip>
                  <Popconfirm
                    title="确定要删除这个应用模板吗？"
                    onConfirm={() => handleDelete([record])}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Tooltip title="删除">
                      <Button type="text" danger icon={<DeleteOutlined />} />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={appTemplates}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={{
            current: queryParams.pageIndex,
            pageSize: queryParams.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 应用模板表单模态框 */}
      <AppTemplateFormModal
        visible={showModal}
        type={modalType}
        record={currentRecord}
        onSuccess={handleSuccess}
        onCancel={closeModal}
      />
    </div>
  );
};

export default AppTemplateManagement;
