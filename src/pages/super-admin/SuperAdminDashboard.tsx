import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Progress } from 'antd';
import { Server, Users, DollarSign, TrendingUp, Activity, AlertTriangle } from 'lucide-react';
import { getDeviceStats } from '@/services/deviceService';
import { formatPrice } from '@/utils/format';

const SuperAdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await getDeviceStats();
        if (response.success) {
          setStats(response.data);
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // 模拟最近活动数据
  const recentActivities = [
    {
      key: '1',
      time: '2024-01-20 10:30',
      action: '新设备接入',
      details: 'AI训练服务器-15 已成功接入平台',
      status: 'success',
    },
    {
      key: '2',
      time: '2024-01-20 09:15',
      action: '用户注册',
      details: '新用户 user123 完成注册',
      status: 'info',
    },
    {
      key: '3',
      time: '2024-01-20 08:45',
      action: '设备维护',
      details: '渲染节点-04 进入维护状态',
      status: 'warning',
    },
    {
      key: '4',
      time: '2024-01-20 08:20',
      action: '收益结算',
      details: '昨日收益已完成结算，总计 ¥15,680',
      status: 'success',
    },
  ];

  const activityColumns = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120,
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          success: { color: 'green', text: '成功' },
          info: { color: 'blue', text: '信息' },
          warning: { color: 'orange', text: '警告' },
          error: { color: 'red', text: '错误' },
        };
        const config = statusMap[status as keyof typeof statusMap] || statusMap.info;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">超管控制面板</h1>
        <p className="text-gray-600 mt-1">平台运营数据总览</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="设备总数"
              value={stats?.total || 0}
              prefix={<Server className="w-4 h-4 text-blue-500" />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div className="mt-2 text-sm text-gray-500">
              在线: {stats?.online || 0} | 离线: {stats?.offline || 0}
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={1248}
              prefix={<Users className="w-4 h-4 text-green-500" />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div className="mt-2 text-sm text-gray-500">
              本月新增: 156
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收益"
              value={stats?.totalRevenue || 0}
              prefix={<DollarSign className="w-4 h-4 text-orange-500" />}
              valueStyle={{ color: '#fa8c16' }}
              formatter={(value) => formatPrice(Number(value))}
            />
            <div className="mt-2 text-sm text-gray-500">
              本月: ¥45,680
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均利用率"
              value={stats?.avgUtilization ? stats.avgUtilization * 100 : 0}
              prefix={<TrendingUp className="w-4 h-4 text-purple-500" />}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
              precision={1}
            />
            <div className="mt-2 text-sm text-gray-500">
              较上月 +5.2%
            </div>
          </Card>
        </Col>
      </Row>

      {/* 设备状态分布 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="设备状态分布" extra={<Activity className="w-4 h-4" />}>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span>在线设备</span>
                  <span>{stats?.online || 0}/{stats?.total || 0}</span>
                </div>
                <Progress 
                  percent={stats?.total ? (stats.online / stats.total) * 100 : 0} 
                  status="active" 
                  strokeColor="#52c41a"
                />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>维护中设备</span>
                  <span>{stats?.maintenance || 0}/{stats?.total || 0}</span>
                </div>
                <Progress 
                  percent={stats?.total ? (stats.maintenance / stats.total) * 100 : 0} 
                  strokeColor="#fa8c16"
                />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>离线设备</span>
                  <span>{stats?.offline || 0}/{stats?.total || 0}</span>
                </div>
                <Progress 
                  percent={stats?.total ? (stats.offline / stats.total) * 100 : 0} 
                  strokeColor="#ff4d4f"
                />
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="系统告警" extra={<AlertTriangle className="w-4 h-4" />}>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div>
                  <div className="font-medium text-red-800">高负载警告</div>
                  <div className="text-sm text-red-600">3台设备CPU使用率超过90%</div>
                </div>
                <Tag color="red">紧急</Tag>
              </div>
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div>
                  <div className="font-medium text-orange-800">存储空间不足</div>
                  <div className="text-sm text-orange-600">2台设备存储使用率超过85%</div>
                </div>
                <Tag color="orange">警告</Tag>
              </div>
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <div className="font-medium text-blue-800">新版本可用</div>
                  <div className="text-sm text-blue-600">系统有新版本更新可用</div>
                </div>
                <Tag color="blue">信息</Tag>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近活动 */}
      <Card title="最近活动" className="w-full">
        <Table
          columns={activityColumns}
          dataSource={recentActivities}
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  );
};

export default SuperAdminDashboard;
