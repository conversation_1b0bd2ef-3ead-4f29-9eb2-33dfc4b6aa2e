import React, { useEffect, useState } from 'react';
import { Table, Card, Button, Tag, Space, Input, Select, Modal, Form, message, Progress } from 'antd';
import { Search, Plus, Edit, Trash2, DollarSign, TrendingUp } from 'lucide-react';
import {
  getProfitSharingRules,
  createProfitSharingRule,
  updateProfitSharingRule,
  deleteProfitSharingRule,
  getProfitSharingStats,
  ProfitSharingRule
} from '@/services/profitSharingService';
import { formatPrice } from '@/utils/format';
import { toast } from 'sonner';

const { Option } = Select;

const ProfitSharingManagement: React.FC = () => {
  const [rules, setRules] = useState<ProfitSharingRule[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<ProfitSharingRule | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchRules();
    fetchStats();
  }, [typeFilter]);

  const fetchRules = async () => {
    setLoading(true);
    try {
      const response = await getProfitSharingRules({
        type: typeFilter || undefined,
      });
      if (response.success) {
        setRules(response.data.items);
      }
    } catch (error) {
      toast.error('获取分润规则失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await getProfitSharingStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  const handleCreate = () => {
    setEditingRule(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (rule: ProfitSharingRule) => {
    setEditingRule(rule);
    form.setFieldsValue({
      ...rule,
      platformRate: rule.platformRate * 100,
      providerRate: rule.providerRate * 100,
    });
    setModalVisible(true);
  };

  const handleDelete = (rule: ProfitSharingRule) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除分润规则 "${rule.name}" 吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteProfitSharingRule(rule.id);
          toast.success('分润规则删除成功');
          fetchRules();
        } catch (error) {
          toast.error('分润规则删除失败');
        }
      },
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      const ruleData = {
        ...values,
        platformRate: values.platformRate / 100,
        providerRate: values.providerRate / 100,
        isActive: true,
      };

      if (editingRule) {
        await updateProfitSharingRule(editingRule.id, ruleData);
        toast.success('分润规则更新成功');
      } else {
        await createProfitSharingRule(ruleData);
        toast.success('分润规则创建成功');
      }

      setModalVisible(false);
      fetchRules();
    } catch (error) {
      toast.error(editingRule ? '分润规则更新失败' : '分润规则创建失败');
    }
  };

  const columns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value: any, record: ProfitSharingRule) =>
        record.name.toLowerCase().includes(value.toLowerCase()),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => {
        const typeMap = {
          global: { color: 'blue', text: '全局' },
          team: { color: 'green', text: '团队' },
          instance: { color: 'orange', text: '实例' },
        };
        const config = typeMap[type as keyof typeof typeMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '平台抽成',
      dataIndex: 'platformRate',
      key: 'platformRate',
      width: 120,
      render: (rate: number) => (
        <div>
          <div>{(rate * 100).toFixed(1)}%</div>
          <Progress
            percent={rate * 100}
            size="small"
            strokeColor="#ff4d4f"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '提供方分成',
      dataIndex: 'providerRate',
      key: 'providerRate',
      width: 120,
      render: (rate: number) => (
        <div>
          <div>{(rate * 100).toFixed(1)}%</div>
          <Progress
            percent={rate * 100}
            size="small"
            strokeColor="#52c41a"
            showInfo={false}
          />
        </div>
      ),
    },

    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: ProfitSharingRule) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<Edit className="w-4 h-4" />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            size="small"
            danger
            icon={<Trash2 className="w-4 h-4" />}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">分润管理</h1>
          <p className="text-gray-600 mt-1">管理平台分润规则和统计</p>
        </div>
        <Button type="primary" icon={<Plus className="w-4 h-4" />} onClick={handleCreate}>
          新建规则
        </Button>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">平台总收益</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatPrice(stats.totalPlatformRevenue)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-red-500" />
            </div>
          </Card>
          <Card>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">提供方总收益</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatPrice(stats.totalProviderRevenue)}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </Card>

          <Card>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">平均抽成比例</p>
                <p className="text-2xl font-bold text-purple-600">
                  {(stats.avgPlatformRate * 100).toFixed(1)}%
                </p>
              </div>
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-bold">%</span>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* 筛选器 */}
      <Card>
        <div className="flex flex-wrap gap-4 items-center">
          <Input
            placeholder="搜索规则名称"
            prefix={<Search className="w-4 h-4 text-gray-400" />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder="规则类型"
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="global">全局</Option>
            <Option value="team">团队</Option>
            <Option value="instance">实例</Option>
          </Select>
          <Button onClick={fetchRules}>刷新</Button>
        </div>
      </Card>

      {/* 规则列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={rules}
          loading={loading}
          rowKey="id"
          pagination={{
            total: rules.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条规则`,
          }}
        />
      </Card>

      {/* 创建/编辑规则模态框 */}
      <Modal
        title={editingRule ? '编辑分润规则' : '新建分润规则'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="规则类型"
            rules={[{ required: true, message: '请选择规则类型' }]}
          >
            <Select placeholder="请选择规则类型">
              <Option value="global">全局规则</Option>
              <Option value="team">团队规则</Option>
              <Option value="instance">实例规则</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="targetId"
            label="目标ID（可选）"
            help="团队或实例的ID，全局规则无需填写"
          >
            <Input placeholder="请输入目标ID" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="platformRate"
              label="平台抽成比例（%）"
              rules={[
                { required: true, message: '请输入平台抽成比例' },
                { type: 'number', min: 0, max: 100, message: '比例必须在0-100之间' }
              ]}
            >
              <Input type="number" min={0} max={100} placeholder="如: 15" />
            </Form.Item>
            <Form.Item
              name="providerRate"
              label="提供方分成比例（%）"
              rules={[
                { required: true, message: '请输入提供方分成比例' },
                { type: 'number', min: 0, max: 100, message: '比例必须在0-100之间' }
              ]}
            >
              <Input type="number" min={0} max={100} placeholder="如: 85" />
            </Form.Item>
          </div>


        </Form>
      </Modal>
    </div>
  );
};

export default ProfitSharingManagement;
