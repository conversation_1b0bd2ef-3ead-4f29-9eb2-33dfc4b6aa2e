import React, { useEffect, useState } from 'react';
import { Table, Card, Button, Tag, Space, Input, Select, Modal, message, Tooltip } from 'antd';
import { Search, Server, Settings, Wifi, WifiOff, Wrench, Eye, Terminal } from 'lucide-react';
import { getDevices, updateDeviceStatus, forceOfflineDevice, assignTenant } from '@/services/deviceService';
import { Device } from '@/data/mockData';
import { formatPrice, formatStatus } from '@/utils/format';
import { toast } from 'sonner';

const { Option } = Select;

const DeviceManagement: React.FC = () => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    fetchDevices();
  }, [statusFilter, typeFilter]);

  const fetchDevices = async () => {
    setLoading(true);
    try {
      const response = await getDevices({
        status: statusFilter || undefined,
        type: typeFilter || undefined,
      });
      if (response.success) {
        setDevices(response.data.items);
      }
    } catch (error) {
      toast.error('获取设备列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (deviceId: string, newStatus: 'online' | 'offline' | 'maintenance') => {
    try {
      const response = await updateDeviceStatus(deviceId, newStatus);
      if (response.success) {
        toast.success('设备状态更新成功');
        fetchDevices();
      }
    } catch (error) {
      toast.error('设备状态更新失败');
    }
  };

  const handleForceOffline = async (deviceId: string) => {
    Modal.confirm({
      title: '确认强制下架',
      content: '确定要强制下架这台设备吗？此操作会立即停止所有相关实例。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await forceOfflineDevice(deviceId);
          toast.success('设备已强制下架');
          fetchDevices();
        } catch (error) {
          toast.error('强制下架失败');
        }
      },
    });
  };

  const handleViewDetails = (device: Device) => {
    setSelectedDevice(device);
    setModalVisible(true);
  };

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value: any, record: Device) =>
        record.name.toLowerCase().includes(value.toLowerCase()) ||
        record.location.toLowerCase().includes(value.toLowerCase()),
      render: (text: string, record: Device) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.location}</div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Tag color={type === 'GPU' ? 'blue' : 'green'}>{type}</Tag>
      ),
    },
    {
      title: '配置',
      key: 'config',
      width: 200,
      render: (_, record: Device) => (
        <div className="text-sm">
          <div>CPU: {record.cpu}</div>
          <div>内存: {record.memory}</div>
          <div>GPU: {record.gpu}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const { text, color } = formatStatus(status);
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '价格/小时',
      dataIndex: 'pricePerHour',
      key: 'pricePerHour',
      width: 100,
      render: (price: number) => formatPrice(price),
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      width: 120,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 80,
      render: (rating: number) => `${rating}/5.0`,
    },
    {
      title: '订单数',
      dataIndex: 'totalOrders',
      key: 'totalOrders',
      width: 80,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: Device) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<Eye className="w-4 h-4" />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="SSH连接">
            <Button
              type="text"
              size="small"
              icon={<Terminal className="w-4 h-4" />}
              onClick={() => toast.info(`SSH连接: ${record.ip}`)}
            />
          </Tooltip>
          {record.status === 'online' ? (
            <Tooltip title="设为维护">
              <Button
                type="text"
                size="small"
                icon={<Wrench className="w-4 h-4" />}
                onClick={() => handleStatusChange(record.id, 'maintenance')}
              />
            </Tooltip>
          ) : record.status === 'maintenance' ? (
            <Tooltip title="设为在线">
              <Button
                type="text"
                size="small"
                icon={<Wifi className="w-4 h-4" />}
                onClick={() => handleStatusChange(record.id, 'online')}
              />
            </Tooltip>
          ) : null}
          <Tooltip title="强制下架">
            <Button
              type="text"
              size="small"
              danger
              icon={<WifiOff className="w-4 h-4" />}
              onClick={() => handleForceOffline(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">设备管理</h1>
        <p className="text-gray-600 mt-1">管理平台上的所有设备</p>
      </div>

      {/* 筛选器 */}
      <Card>
        <div className="flex flex-wrap gap-4 items-center">
          <Input
            placeholder="搜索设备名称或位置"
            prefix={<Search className="w-4 h-4 text-gray-400" />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder="设备状态"
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="online">在线</Option>
            <Option value="offline">离线</Option>
            <Option value="maintenance">维护中</Option>
          </Select>
          <Select
            placeholder="设备类型"
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="GPU">GPU</Option>
            <Option value="CPU">CPU</Option>
          </Select>
          <Button onClick={fetchDevices}>刷新</Button>
        </div>
      </Card>

      {/* 设备列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={devices}
          loading={loading}
          rowKey="id"
          pagination={{
            total: devices.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 台设备`,
          }}
        />
      </Card>

      {/* 设备详情模态框 */}
      <Modal
        title="设备详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedDevice && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">设备名称</label>
                <p className="mt-1">{selectedDevice.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">设备类型</label>
                <p className="mt-1">{selectedDevice.type}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">CPU</label>
                <p className="mt-1">{selectedDevice.cpu}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">内存</label>
                <p className="mt-1">{selectedDevice.memory}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">GPU</label>
                <p className="mt-1">{selectedDevice.gpu}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">存储</label>
                <p className="mt-1">{selectedDevice.storage}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">位置</label>
                <p className="mt-1">{selectedDevice.location}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">IP地址</label>
                <p className="mt-1">{selectedDevice.ip}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">价格/小时</label>
                <p className="mt-1">{formatPrice(selectedDevice.pricePerHour)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">提供商</label>
                <p className="mt-1">{selectedDevice.provider}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">评分</label>
                <p className="mt-1">{selectedDevice.rating}/5.0</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">总订单数</label>
                <p className="mt-1">{selectedDevice.totalOrders}</p>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DeviceManagement;
