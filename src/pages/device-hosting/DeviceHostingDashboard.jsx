import { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button, Progress } from 'antd';
import { HistoryOutlined } from '@ant-design/icons';
import {
  Server,
  DollarSign,
  TrendingUp,
  Activity,
  Plus,
  Eye,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { getBillingStats } from '@/services/billingService';
import { formatPrice, formatStatus } from '@/utils/format';
import { deviceService } from '@/services/machine';
import { useRequest } from 'ahooks';
import DeviceConnectionDialog from '@/components/DeviceConnectionDialog';

const DeviceHostingDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);

  // 使用 useRequest 管理设备概览统计数据
  const { data: overviewData, refresh: refreshOverview } = useRequest(
    () => deviceService.getOverviewCount(),
    {
      onError: () => {
        console.error('获取设备统计数据失败');
      },
    }
  );

  useEffect(() => {
    fetchData();
  }, [user]);

  const fetchData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // 获取账单统计
      const statsResponse = await getBillingStats(user.id);
      if (statsResponse.success) {
        setStats(statsResponse.data);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 模拟设备收益数据
  const deviceEarnings = [
    {
      id: 1,
      name: 'GPU-Server-001',
      type: 'GPU',
      status: 'online',
      todayEarnings: 156.8,
      totalEarnings: 4520.3,
      utilizationRate: 85,
      location: '上海',
    },
    {
      id: 2,
      name: 'CPU-Server-002',
      type: 'CPU',
      status: 'online',
      todayEarnings: 89.5,
      totalEarnings: 2340.6,
      utilizationRate: 72,
      location: '北京',
    },
    {
      id: 3,
      name: 'GPU-Server-003',
      type: 'GPU',
      status: 'offline',
      todayEarnings: 0,
      totalEarnings: 1890.4,
      utilizationRate: 0,
      location: '深圳',
    },
  ];

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.location}</div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: type => (
        <Tag color={type === 'GPU' ? 'blue' : 'green'}>{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: status => (
        <Tag color={status === 'online' ? 'success' : 'default'}>
          {status === 'online' ? '在线' : '离线'}
        </Tag>
      ),
    },
    {
      title: '利用率',
      dataIndex: 'utilizationRate',
      key: 'utilizationRate',
      render: rate => (
        <div className="w-20">
          <Progress
            percent={rate}
            size="small"
            status={rate > 80 ? 'success' : rate > 50 ? 'normal' : 'exception'}
          />
        </div>
      ),
    },
    {
      title: '今日收益',
      dataIndex: 'todayEarnings',
      key: 'todayEarnings',
      render: earnings => (
        <span className="font-medium text-green-600">
          ¥{earnings.toFixed(2)}
        </span>
      ),
    },
    {
      title: '总收益',
      dataIndex: 'totalEarnings',
      key: 'totalEarnings',
      render: earnings => (
        <span className="font-medium">¥{earnings.toFixed(2)}</span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="link"
          icon={<Eye className="w-4 h-4" />}
          onClick={() => navigate(`/my-instances?id=${record.id}`)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题和操作按钮 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">设备托管概览</h1>
          <p className="text-gray-600 mt-1">管理您的设备托管业务</p>
        </div>
        <div className="flex gap-2">
          <Button
            icon={<Plus className="w-4 h-4" />}
            type="primary"
            onClick={() => setShowConnectionDialog(true)}
          >
            接入新设备
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="托管设备数"
              value={overviewData?.MachineCount}
              prefix={<Server className="w-4 h-4 text-blue-500" />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div className="mt-2 text-sm text-gray-500">
              在线: {overviewData?.OnlineMachineCount}
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日收益"
              value={stats?.todayEarnings || 246.3}
              prefix={<DollarSign className="w-4 h-4 text-green-500" />}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
            <div className="mt-2 text-sm text-gray-500">较昨日 +12.5%</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="本月收益"
              value={stats?.monthlyEarnings || 7890.5}
              prefix={<TrendingUp className="w-4 h-4 text-orange-500" />}
              precision={2}
              valueStyle={{ color: '#fa8c16' }}
            />
            <div className="mt-2 text-sm text-gray-500">较上月 +8.3%</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均利用率"
              value={73}
              suffix="%"
              prefix={<Activity className="w-4 h-4 text-purple-500" />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div className="mt-2 text-sm text-gray-500">设备运行效率</div>
          </Card>
        </Col>
      </Row>

      {/* 设备收益列表 */}
      <Card
        title="设备收益详情"
        extra={
          <Button type="link" onClick={() => navigate('/my-instances')}>
            查看全部
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={deviceEarnings}
          rowKey="id"
          pagination={false}
          loading={loading}
        />
      </Card>

      {/* 设备接入对话框 */}
      <DeviceConnectionDialog
        isOpen={showConnectionDialog}
        onClose={() => setShowConnectionDialog(false)}
        onDeviceAdded={() => {
          refreshOverview();
          setShowConnectionDialog(false);
        }}
      />
    </div>
  );
};

export default DeviceHostingDashboard;
