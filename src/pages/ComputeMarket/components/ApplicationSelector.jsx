import React from 'react';
import { <PERSON>, But<PERSON>, Space, Typography, Tag, Divider } from 'antd';
import { Sparkles, Package, RefreshCcw } from 'lucide-react';

const { Title, Text } = Typography;

const ApplicationSelector = ({
  selectedApplication,
  setSelectedApplication,
  appTemplates,
  onShowApplicationDialog,
}) => {
  return (
    <div>
      <Title level={5} className="!mb-3">
        <Space>
          <Sparkles className="w-4 h-4 text-blue-500" />
          应用场景
        </Space>
      </Title>

      {selectedApplication ? (
        <Card size="small" className="mb-3">
          <div className="flex items-center justify-between">
            <Space>
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                {appTemplates.find(
                  app => app.id === selectedApplication
                )?.iconUrl ? (
                  <img
                    src={
                      appTemplates.find(
                        app => app.id === selectedApplication
                      )?.iconUrl
                    }
                    alt="app icon"
                    className="w-6 h-6 rounded"
                  />
                ) : (
                  <Package className="w-4 h-4 text-blue-600" />
                )}
              </div>
              <div>
                <Text strong>
                  {
                    appTemplates.find(
                      app => app.id === selectedApplication
                    )?.name
                  }
                </Text>
                <div>
                  <Text type="secondary" className="text-xs">
                    v
                    {
                      appTemplates.find(
                        app => app.id === selectedApplication
                      )?.version
                    }
                  </Text>
                </div>
              </div>
            </Space>
            <Button
              type="text"
              size="small"
              onClick={() => setSelectedApplication('')}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </Button>
          </div>
          <Divider className="!my-2" />

          {/* 应用描述和标签 */}
          <div>
            {(() => {
              const app = appTemplates.find(
                a => a.id === selectedApplication
              );
              if (!app) return null;

              return (
                <>
                  {/* 应用描述 */}
                  {app.description && (
                    <div className="mb-2">
                      <Text type="secondary" className="text-xs" title={app.description}>
                        {app.description.length > 60 ? `${app.description.slice(0, 60)}...` : app.description}
                      </Text>
                    </div>
                  )}

                  {/* 应用标签 */}
                  {app.tags && (
                    <div>
                      <div className="flex flex-wrap gap-1">
                        {(typeof app.tags === 'string'
                          ? app.tags.split(',')
                          : app.tags || []
                        ).slice(0, 3).map((tag, index) => (
                          <Tag
                            bordered={false}
                            key={index}
                            color="cyan"
                            size="small"
                          >
                            {typeof tag === 'string' ? tag.trim() : tag}
                          </Tag>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              );
            })()}
          </div>

          <Button
            type="primary"
            icon={<RefreshCcw className="w-4 h-4" />}
            block
            onClick={onShowApplicationDialog}
            className="mt-3"
          >
            更换应用
          </Button>
        </Card>
      ) : (
        <Button
          type="dashed"
          size="middle"
          block
          onClick={onShowApplicationDialog}
          icon={<Sparkles className="w-4 h-4" />}
        >
          选择应用
        </Button>
      )}
    </div>
  );
};

export default ApplicationSelector;