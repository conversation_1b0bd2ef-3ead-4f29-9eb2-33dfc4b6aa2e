import React from 'react';
import {
  <PERSON>,
  Button,
  Tag,
  Spin,
  Empty,
  Typography,
  Space,
  Row,
  Col,
  Pagination,
  Tooltip,
} from 'antd';
import {
  Server,
  MapPin,
  Cpu,
  HardDrive,
  Zap,
  ShoppingCart,
  Sparkles,
  DollarSign,
  Clock,
  Gauge,
} from 'lucide-react';
import { RainbowButton } from '@/components/magicui/rainbow-button';
import PriceInfo from '@/components/PriceInfo';

const { Title, Text } = Typography;

const ResourceList = ({
  filteredDevices,
  marketLoading,
  pagination,
  setPagination,
  marketData,
  handleRentDevice,
  onShowAIDialog,
  regionList = [], // 地理位置列表数据
}) => {
  // 根据regionCode获取地区名称
  const getRegionName = (regionCode) => {
    const region = regionList.find(r => r.id == regionCode || r.regionName === regionCode);
    return region ? region.regionName : regionCode;
  };

  const renderDeviceCard = (device) => (
    <Card
      key={device.id}
      className="w-full transition-all duration-300 hover:shadow-md hover:-translate-y-1 bg-white border border-gray-200/60  mb-4"
      styles={{
        body: { padding: '24px' }
      }}
    >
      <Row gutter={24} align="bottom">
        {/* 左侧：设备基本信息 + 硬件配置（纵向排列） */}
        <Col xs={24} sm={24} md={16} lg={18}>
          <div className="space-y-6">
            {/* 设备基本信息 */}
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-sm">
                  <Server className="w-5 h-5 text-white" />
                </div>
                <div>
                  <Title level={5} className="!mb-0 text-gray-900 font-semibold">
                    {device.machineName || device.machineAlias}
                  </Title>
                  <Text type="secondary" className="text-xs">
                    {device.machineCode}
                  </Text>
                </div>
                <div className="ml-auto flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-3 h-3 text-gray-400" />
                    <Text type="secondary" className="text-xs">
                      {getRegionName(device.regionCode)}
                    </Text>
                  </div>
                  <Tag
                    color={device.machineType === 'GPU' ? 'blue' : 'green'}
                    className="text-xs font-medium"
                  >
                    {device.machineType}
                  </Tag>
                </div>
              </div>


            </div>

            {/* 硬件配置信息 */}
            <div className="bg-gradient-to-br from-gray-50 to-blue-50/50 rounded-xl p-4 border border-gray-100 shadow-sm">
              <Title level={5} className="!mb-3 text-gray-800 flex items-center gap-2">
                <Gauge className="w-4 h-4 text-blue-500" />
                硬件配置
              </Title>
              <Row gutter={[16, 12]}>
                <Col span={12}>
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 bg-white rounded-lg shadow-sm border border-blue-100">
                      <Cpu className="w-4 h-4 text-blue-500" />
                    </div>
                    <div>
                      <Text className="text-xs text-gray-500 block">CPU</Text>
                      <Text className="text-sm font-semibold text-gray-900">{device.cpuCode}</Text>
                      <Text type="secondary" className="text-xs">({device.cpuNum}核)</Text>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 bg-white rounded-lg shadow-sm border border-green-100">
                      <HardDrive className="w-4 h-4 text-green-500" />
                    </div>
                    <div>
                      <Text className="text-xs text-gray-500 block">内存</Text>
                      <Text className="text-sm font-semibold text-gray-900">{device.memorySize}GB</Text>
                    </div>
                  </div>
                </Col>
                {device.gpuNum > 0 && (
                  <Col span={12}>
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 bg-white rounded-lg shadow-sm border border-orange-100">
                        <Zap className="w-4 h-4 text-orange-500" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500 block">GPU</Text>
                        <Text className="text-sm font-semibold text-gray-900">{device.gpuName}</Text>
                        <Text type="secondary" className="text-xs">x{device.gpuNum}</Text>
                      </div>
                    </div>
                  </Col>
                )}
                <Col span={12}>
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 bg-white rounded-lg shadow-sm border border-purple-100">
                      <Server className="w-4 h-4 text-purple-500" />
                    </div>
                    <div>
                      <Text className="text-xs text-gray-500 block">存储</Text>
                      <Text className="text-sm font-semibold text-gray-900">{device.diskSize}GB</Text>
                      <Text type="secondary" className="text-xs">{device.diskType}</Text>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </Col>

        {/* 右侧：价格信息和操作 */}
        <Col xs={24} sm={24} md={8} lg={6} >
          <div className="text-center space-y-4 flex flex-col justify-end">
            {/* 价格信息 */}
            <div className="mt-auto bg-gradient-to-br from-blue-50 to-white rounded-xl p-4 border border-blue-200/50 shadow-sm">
              <PriceInfo
                pricingList={device.resourcePricings || []}
                displayStyle="detailed"
                placement="left"
                trigger="hover"
              />
            </div>

            {/* 操作按钮 */}
            <Button
              type="primary"
              block
              onClick={() => handleRentDevice(device)}
              icon={<ShoppingCart className="w-5 h-5" />}
              className="bg-gradient-to-r border-0 shadow-lg hover:shadow-xl transition-all duration-300 text-sm h-10"
            >
              立即租用
            </Button>
          </div>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Server className="w-6 h-6 text-blue-600" />
          <Title level={3} className="!mb-0 text-gray-800">
            可用算力资源
          </Title>
          <Text type="secondary">
            共 {marketData?.total || 0} 个资源
          </Text>
        </div>
        <RainbowButton
          className="rounded-lg bg-primary shadow-md hover:shadow-lg"
          onClick={onShowAIDialog}
        >
          <Sparkles className="w-4 h-4" />
          AI智能推荐
        </RainbowButton>
      </div>

      {/* 资源列表 */}
      {marketLoading ? (
        <div className="text-center py-16">
          <Spin size="large" />
          <div className="mt-4">
            <Title level={4} className="text-gray-600">正在加载算力资源...</Title>
            <Text type="secondary">请稍候</Text>
          </div>
        </div>
      ) : filteredDevices.length === 0 ? (
        <div className="text-center py-16">
          <Empty
            image={<Server className="w-20 h-20 text-gray-300 mx-auto mb-4" />}
            description={
              <div>
                <Title level={4} className="text-gray-500 mb-2">暂无匹配的设备</Title>
                <Text type="secondary">请尝试调整筛选条件</Text>
              </div>
            }
          />
        </div>
      ) : (
        <>
          {/* 设备列表 - 每行一个 */}
          <div className="space-y-4">
            {filteredDevices.map(device => renderDeviceCard(device))}
          </div>

          {/* 分页 */}
          <div className="flex justify-center pt-6">
            <Pagination
              current={pagination.pageIndex}
              pageSize={pagination.pageSize}
              total={marketData?.total || 0}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
              onChange={(page, pageSize) => {
                setPagination({ pageIndex: page, pageSize });
              }}
              className="bg-white/60 p-4 rounded-lg shadow-sm"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ResourceList;