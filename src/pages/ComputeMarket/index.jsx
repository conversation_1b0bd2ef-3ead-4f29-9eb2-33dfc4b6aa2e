import React, { useState, useMemo, useEffect } from 'react';
import { useSearchParams, useNavigate, useLocation } from 'react-router-dom';
import { Row, Col } from 'antd';
import { useRequest } from 'ahooks';

import { useInstances } from '@/contexts/InstanceContext';
import { toast } from 'sonner';
import { getGpuList, getCpuList, getRegionList } from '@/services/enums';
import { getMarketList } from '@/services/market';
import { appTemplateService } from '@/services/appTemplate';
import PageHeader from '@/components/PageHeader';
import AIRecommendationDialog from '@/components/AIRecommendationDialog';
import InstanceOrderWizard from '@/components/InstanceOrderWizard';
import FilterPanel from './components/FilterPanel';
import ResourceList from './components/ResourceList';
import ApplicationSelector from './components/ApplicationSelector';
import ApplicationSelectionDialog from '../../components/ApplicationSelectionDialog';


const ComputeMarket = () => {
  const { addInstance } = useInstances();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState('');
  const [selectedGpuModel, setSelectedGpuModel] = useState('all');
  const [selectedCpuModel, setSelectedCpuModel] = useState('all');
  const [showAIDialog, setShowAIDialog] = useState(false);
  const [showOrderWizard, setShowOrderWizard] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);
  const [appTemplates, setAppTemplates] = useState([]);
  const [appTemplatesLoading, setAppTemplatesLoading] = useState(false);

  // 分页参数
  const [pagination, setPagination] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  // 获取 GPU 列表
  const { data: gpuList = [], loading: gpuLoading } = useRequest(getGpuList, {
    onError: error => {
      console.error('获取 GPU 列表失败:', error);
    },
  });

  // 获取 CPU 列表
  const { data: cpuList = [], loading: cpuLoading } = useRequest(getCpuList, {
    onError: error => {
      console.error('获取 CPU 列表失败:', error);
    },
  });

  // 获取区域列表
  const { data: regionList = [], loading: regionLoading } = useRequest(
    getRegionList,
    {
      onError: error => {
        console.error('获取区域列表失败:', error);
      },
    }
  );

  // 构建查询参数
  const buildQueryParams = useMemo(() => {
    const params = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
    };

    // 添加搜索条件
    if (searchTerm) {
      params.machineName = searchTerm;
    }

    // 添加设备类型
    if (selectedType !== 'all') {
      params.machineType = selectedType;
    }

    // 添加区域筛选
    if (selectedLocation !== 'all') {
      const region = regionList.find(r => r.regionName === selectedLocation);
      if (region) {
        params.regionCode = region.id;
      }
    }

    // 添加 GPU 型号筛选
    if (selectedGpuModel !== 'all') {
      const gpu = gpuList.find(g => g.gpuName === selectedGpuModel);
      if (gpu) {
        params.code = gpu.gpuCode;
        if (selectedType === 'GPU') {
          params.minGpuCode = gpu.gpuCode;
        }
      }
    }

    // 添加 CPU 型号筛选
    if (selectedCpuModel !== 'all') {
      const cpu = cpuList.find(c => c.cpuName === selectedCpuModel);
      if (cpu) {
        params.code = cpu.cpuCode;
      }
    }

    // 根据选择的应用添加硬件要求参数
    if (selectedApplication) {
      params.appId = selectedApplication;
    }

    return params;
  }, [
    pagination,
    searchTerm,
    selectedType,
    selectedLocation,
    selectedGpuModel,
    selectedCpuModel,
    selectedApplication,
    regionList,
    gpuList,
    cpuList,
  ]);

  // 获取算力市场列表
  const {
    data: marketData,
    loading: marketLoading,
    refresh: refreshMarket,
  } = useRequest(() => getMarketList(buildQueryParams), {
    refreshDeps: [JSON.stringify(buildQueryParams)],
    retryCount: 3,
    retryInterval: 1000,
  });

  // 加载应用模板列表
  const loadAppTemplates = async () => {
    setAppTemplatesLoading(true);
    try {
      const data = await appTemplateService.getAppTemplates({
        pageIndex: 1,
        pageSize: 100,
        status: 1, // 只获取启用的应用
      });

      setAppTemplates(data.records || []);
    } catch (error) {
      console.error('加载应用模板失败:', error);
    } finally {
      setAppTemplatesLoading(false);
    }
  };

  // 处理从应用市场跳转过来的应用参数
  useEffect(() => {
    const state = location.state;
    if (state?.selectedApplication) {
      setSelectedApplication(state.selectedApplication.id);
    }

    const appId = searchParams.get('app');
    if (appId) {
      setSelectedApplication(appId);
    }
  }, [searchParams, location.state]);

  // 加载应用模板
  useEffect(() => {
    loadAppTemplates();
  }, []);

  // 直接使用接口返回的数据，不做转换
  const availableDevices = useMemo(() => {
    if (!marketData?.records) return [];
    return marketData.records;
  }, [marketData]);

  // 重置筛选条件
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedLocation('all');
    setSelectedApplication('');
    setSelectedGpuModel('all');
    setSelectedCpuModel('all');
  };

  const handleRentDevice = device => {
    setSelectedDevice(device);
    setShowOrderWizard(true);
  };

  const handleConfirmOrder = async orderConfig => {
    try {
      const selectedApp = orderConfig.application;
      const device = orderConfig.device;

      // 创建实例数据
      const instanceData = {
        name: orderConfig.instanceName,
        deviceId: device.id,
        status: orderConfig.deploymentId ? 'deploying' : 'running', // 如果有部署ID，状态为部署中
        cpu: device.cpuCode,
        memory: `${device.memorySize}GB`,
        gpu: device.gpuName,
        storage: `${device.diskSize}GB ${device.diskType}`,
        pricePerHour: orderConfig.pricePerHour,
        totalCost: orderConfig.totalCost,
        startTime: new Date().toISOString(),
        userId: '1',
        applications: selectedApp ? [selectedApp.id] : [],
        applicationInfo: selectedApp
          ? {
            id: selectedApp.id,
            name: selectedApp.name,
            icon: selectedApp.iconUrl,
            version: selectedApp.version,
            status: orderConfig.deploymentId ? 'deploying' : 'running',
            ports: selectedApp.protocolPortsList?.map(p => p.port) || [8080],
            accessUrl: orderConfig.deploymentId
              ? null // 部署中时暂无访问地址
              : `http://${device.innerIp}:${selectedApp.protocolPortsList?.[0]?.port || 8080
              }`,
            pricePerHour: selectedApp.pricePerHour || 0,
            installTime: new Date().toISOString(),
            config: orderConfig.appConfig,
            envVars: orderConfig.envVars,
            deploymentId: orderConfig.deploymentId, // 保存部署ID用于后续查询状态
          }
          : undefined,
        sshInfo: {
          host: device.innerIp,
          port: 22000 + parseInt(device.id),
          username: 'user1',
        },
        // 保存订购配置信息
        orderConfig: {
          duration: orderConfig.duration,
          gpuCount: orderConfig.gpuCount,
          extraDiskSize: orderConfig.extraDiskSize,
          autoRenew: orderConfig.autoRenew,
        },
      };

      // 先添加实例到状态中
      const newInstance = await addInstance(instanceData);

      // 关闭对话框
      setShowOrderWizard(false);

      // 显示成功消息
      if (orderConfig.deploymentId) {
        toast.success('实例创建成功，应用正在部署中，请稍候...');
      } else {
        toast.success('实例创建成功！');
      }

      // 跳转到我的实例页面
      navigate(`/my-instances?id=${newInstance.id}`);
    } catch (error) {
      console.error('订购失败:', error);
    }
  };

  return (
    <div className="min-h-screen">
      {/* 页面标题和AI推荐按钮 */}
      <div className="mb-6">

        <PageHeader
          title="算力市场 "
          subtitle="发现和租用最适合您需求的计算资源"
        />

      </div>

      {/* 主要内容区域：左侧筛选，右侧资源列表 */}
      <Row gutter={[24, 24]}>
        {/* 左侧筛选面板 */}
        <Col xs={24} lg={6}>
          <FilterPanel
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            selectedType={selectedType}
            setSelectedType={setSelectedType}
            selectedLocation={selectedLocation}
            setSelectedLocation={setSelectedLocation}
            selectedGpuModel={selectedGpuModel}
            setSelectedGpuModel={setSelectedGpuModel}
            selectedCpuModel={selectedCpuModel}
            setSelectedCpuModel={setSelectedCpuModel}
            regionList={regionList}
            regionLoading={regionLoading}
            gpuList={gpuList}
            gpuLoading={gpuLoading}
            cpuList={cpuList}
            cpuLoading={cpuLoading}
            resetFilters={resetFilters}
          >
            <ApplicationSelector
              selectedApplication={selectedApplication}
              setSelectedApplication={setSelectedApplication}
              appTemplates={appTemplates}
              onShowApplicationDialog={() => setShowApplicationDialog(true)}
            />
          </FilterPanel>
        </Col>

        {/* 右侧资源列表 */}
        <Col xs={24} lg={18}>
          <ResourceList
            filteredDevices={availableDevices}
            marketLoading={marketLoading}
            pagination={pagination}
            setPagination={setPagination}
            marketData={marketData}
            handleRentDevice={handleRentDevice}
            onShowAIDialog={() => setShowAIDialog(true)}
            regionList={regionList}
          />
        </Col>
      </Row>

      {/* 应用选择对话框 */}
      <ApplicationSelectionDialog
        visible={showApplicationDialog}
        onClose={() => setShowApplicationDialog(false)}
        onSelect={(appId) => {
          setSelectedApplication(appId);
          setShowApplicationDialog(false);
        }}
      />

      {/* AI推荐对话框 */}
      <AIRecommendationDialog
        isOpen={showAIDialog}
        onClose={() => setShowAIDialog(false)}
        onRentDevice={handleRentDevice}
      />

      {/* 实例订购向导 */}
      <InstanceOrderWizard
        visible={showOrderWizard}
        onCancel={() => setShowOrderWizard(false)}
        device={selectedDevice}
        application={
          selectedApplication
            ? appTemplates.find(app => app.id === selectedApplication)
            : null
        }
        title="订购算力实例"
        onConfirm={handleConfirmOrder}
      />
    </div>
  );
};

export default ComputeMarket;
