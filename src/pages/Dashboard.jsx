import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Statistic, Button, Timeline, Typography } from 'antd';
import ReactECharts from 'echarts-for-react';
import {
  Server,
  Cpu,
  DollarSign,
  TrendingUp,
  ShoppingCart,
  HardDrive,
  Store,
  User,
  ArrowUpRight,
} from 'lucide-react';
import PageHeader from '@/components/PageHeader';

const { Text } = Typography;

const Dashboard = () => {
  const navigate = useNavigate();

  // Mock data
  const stats = [
    {
      title: '运行实例',
      value: 3,
      suffix: '个',
      description: '当前活跃实例数量',
      icon: <Server className="w-6 h-6 text-blue-600" />,
      trend: 12.5,
    },
    {
      title: '总算力',
      value: 48,
      suffix: '核',
      description: '已分配的计算核心',
      icon: <Cpu className="w-6 h-6 text-blue-600" />,
      trend: 8.2,
    },
    {
      title: '本月支出',
      value: 1248,
      prefix: '¥',
      description: '当月算力费用',
      icon: <DollarSign className="w-6 h-6 text-blue-600" />,
      trend: -5.3,
    },
    {
      title: '资源利用率',
      value: 86,
      suffix: '%',
      description: '平均资源使用率',
      icon: <TrendingUp className="w-6 h-6 text-blue-600" />,
      trend: 15.8,
    },
  ];

  // Cost trend data
  const costTrendData = [890, 1120, 980, 1350, 1180, 1248];
  const months = ['1月', '2月', '3月', '4月', '5月', '6月'];

  // Instance status data
  const instanceStatusData = [
    { name: '运行中', value: 3, itemStyle: { color: '#1890ff' } },
    { name: '已停止', value: 2, itemStyle: { color: '#91d5ff' } },
    { name: '维护中', value: 1, itemStyle: { color: '#e6f7ff' } },
  ];

  // Cost trend chart options
  const costTrendOption = {
    tooltip: {
      trigger: 'axis',
      formatter: params => {
        return `${params[0].name}<br/>费用: ¥${params[0].value}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8',
        },
      },
      axisLabel: {
        color: '#666',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
      axisLabel: {
        color: '#666',
      },
    },
    series: [
      {
        name: '费用',
        type: 'line',
        smooth: true,
        data: costTrendData,
        lineStyle: {
          color: '#1890ff',
          width: 3,
        },
        itemStyle: {
          color: '#1890ff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.2)',
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0)',
              },
            ],
          },
        },
      },
    ],
  };

  // Instance status pie chart options
  const instanceStatusOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      left: 'center',
      textStyle: {
        color: '#666',
      },
    },
    series: [
      {
        name: '实例状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: instanceStatusData,
      },
    ],
  };

  return (
    <div className="min-h-screen font-sans">
      {/* Page Header */}
      <PageHeader title="控制面板" subtitle="欢迎使用帆一异构智算云平台" />

      {/* Stat Cards */}
      <Row gutter={[24, 24]} className="mb-6">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card
              hoverable
              className="h-full rounded-lg border-none shadow-sm transition-shadow hover:shadow-md"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center mr-4">
                    {stat.icon}
                  </div>
                  <div>
                    <Statistic
                      title={<Text type="secondary">{stat.title}</Text>}
                      value={stat.value}
                      prefix={stat.prefix}
                      suffix={stat.suffix}
                      valueStyle={{
                        color: '#1a237e',
                        fontSize: '22px',
                        fontWeight: '600',
                      }}
                    />
                  </div>
                </div>
                <div
                  className={`text-sm ${
                    stat.trend > 0 ? 'text-green-500' : 'text-red-500'
                  }`}
                >
                  <ArrowUpRight
                    className={`w-4 h-4 inline ${
                      stat.trend < 0 ? 'transform rotate-90' : ''
                    }`}
                  />
                  {Math.abs(stat.trend)}%
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Charts */}
      <Row gutter={[24, 24]} className="mb-6">
        <Col xs={24} lg={16}>
          <Card
            title="费用趋势"
            extra={<Button type="link">查看详情</Button>}
            className="rounded-lg border-none shadow-sm"
          >
            <ReactECharts
              option={costTrendOption}
              style={{ height: '300px' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title="实例状态"
            extra={<Button type="link">管理实例</Button>}
            className="rounded-lg border-none shadow-sm"
          >
            <ReactECharts
              option={instanceStatusOption}
              style={{ height: '300px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Card title="快速操作" className="rounded-lg border-none shadow-sm mb-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {[
            {
              title: '租用实例',
              desc: '浏览算力市场',
              icon: <ShoppingCart className="w-6 h-6 text-blue-600" />,
              path: '/compute-market',
            },
            {
              title: '托管设备',
              desc: '接入您的设备',
              icon: <HardDrive className="w-6 h-6 text-blue-600" />,
              path: '/device-hosting',
            },
            {
              title: '应用市场',
              desc: '一键部署应用',
              icon: <Store className="w-6 h-6 text-blue-600" />,
              path: '/app-market',
            },
            {
              title: '查看账单',
              desc: '费用明细',
              icon: <User className="w-6 h-6 text-blue-600" />,
              path: '/profile',
            },
          ].map(action => (
            <div
              key={action.title}
              onClick={() => navigate(action.path)}
              className="group cursor-pointer p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-all duration-300 text-center"
            >
              <div className="w-12 h-12 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center transition-colors">
                {action.icon}
              </div>
              <h3 className="font-medium text-gray-800 mb-1">{action.title}</h3>
              <p className="text-sm text-gray-500">{action.desc}</p>
            </div>
          ))}
        </div>
      </Card>

      {/* Recent Activities */}
      <Card
        title="最近活动"
        extra={<Button type="link">查看全部</Button>}
        className="rounded-lg border-none shadow-sm"
      >
        <Timeline
          items={[
            {
              color: 'blue',
              children: (
                <div>
                  <div className="font-medium">DeepSeek训练实例已启动</div>
                  <Text type="secondary" className="text-xs">
                    2小时前
                  </Text>
                </div>
              ),
            },
            {
              color: 'blue',
              children: (
                <div>
                  <div className="font-medium">安装了Jupyter Lab应用</div>
                  <Text type="secondary" className="text-xs">
                    4小时前
                  </Text>
                </div>
              ),
            },
            {
              color: 'blue',
              children: (
                <div>
                  <div className="font-medium">图像渲染实例已停止</div>
                  <Text type="secondary" className="text-xs">
                    1天前
                  </Text>
                </div>
              ),
            },
            {
              color: 'gray',
              children: (
                <div>
                  <div className="font-medium">创建了新的存储卷</div>
                  <Text type="secondary" className="text-xs">
                    2天前
                  </Text>
                </div>
              ),
            },
          ]}
        />
      </Card>
    </div>
  );
};

export default Dashboard;
