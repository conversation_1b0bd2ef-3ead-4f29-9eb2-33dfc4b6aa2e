import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Server, User, Shield, Mail, Lock, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';
import { WarpBackground } from '@/components/magicui/warp-background';

const Login: React.FC = () => {
  const { user, login, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (user) {
    return <Navigate to="/" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      toast.error('请填写所有字段');
      return;
    }

    setIsSubmitting(true);
    try {
      const success = await login(email, password);
      if (success) {
        toast.success('登录成功');
      } else {
        toast.error('登录失败，请检查邮箱和密码');
      }
    } catch (error) {
      toast.error('登录过程中发生错误');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleQuickLogin = async (userType: 'user' | 'admin') => {
    setIsSubmitting(true);
    try {
      const credentials = userType === 'admin'
        ? { email: '<EMAIL>', password: 'admin123' }
        : { email: '<EMAIL>', password: 'user123' };

      const success = await login(credentials.email, credentials.password);
      if (success) {
        toast.success(`${userType === 'admin' ? '管理员' : '普通用户'}登录成功`);
      } else {
        toast.error('快捷登录失败');
      }
    } catch (error) {
      toast.error('登录过程中发生错误');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <WarpBackground className='h-screen flex items-center justify-center'>
      {/* 主登录卡片 */}
      <Card className="shadow-xl border-0 bg-white w-[446px]">
        <CardHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
              <Server className="w-7 h-7 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 mb-1">
            SailFusion Cloud
          </CardTitle>
          <CardDescription className="text-gray-600">
            帆一异构智算云平台
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 快捷登录按钮 */}
          <div className="grid grid-cols-2 gap-3">
            <Button
              onClick={() => handleQuickLogin('user')}
              disabled={isSubmitting}
              variant="outline"
              className="h-11"
            >
              <User className="w-4 h-4 mr-2" />
              普通用户
            </Button>
            <Button
              onClick={() => handleQuickLogin('admin')}
              disabled={isSubmitting}
              variant="outline"
              className="h-11"
            >
              <Shield className="w-4 h-4 mr-2" />
              管理员
            </Button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-white text-gray-500">或使用邮箱登录</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 h-11"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="password"
                  type="password"
                  placeholder="密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 h-11"
                  required
                />
              </div>
            </div>
            <Button
              type="submit"
              className="w-full h-11"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  登录中...
                </div>
              ) : (
                <div className="flex items-center">
                  登录
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </WarpBackground>
  );
};

export default Login;
