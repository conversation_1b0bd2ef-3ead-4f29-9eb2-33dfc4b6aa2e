/**
 * 模拟网络请求的工具类
 */

// 模拟网络延时
export const delay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 模拟API响应格式
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  code?: number;
}

// 模拟成功响应
export const createSuccessResponse = <T>(data: T, message?: string): ApiResponse<T> => ({
  success: true,
  data,
  message: message || '操作成功',
  code: 200,
});

// 模拟错误响应
export const createErrorResponse = (message: string, code: number = 400): ApiResponse<null> => ({
  success: false,
  data: null,
  message,
  code,
});

// 模拟分页响应
export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message?: string;
}

export const createPaginatedResponse = <T>(
  items: T[],
  page: number = 1,
  pageSize: number = 10,
  message?: string
): PaginatedResponse<T> => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedItems = items.slice(startIndex, endIndex);
  
  return {
    success: true,
    data: {
      items: paginatedItems,
      total: items.length,
      page,
      pageSize,
      totalPages: Math.ceil(items.length / pageSize),
    },
    message: message || '获取成功',
  };
};

// 模拟随机失败
export const shouldSimulateError = (errorRate: number = 0.1): boolean => {
  return Math.random() < errorRate;
};

// 模拟网络请求
export const mockRequest = async <T>(
  data: T,
  options: {
    delay?: number;
    errorRate?: number;
    errorMessage?: string;
  } = {}
): Promise<ApiResponse<T>> => {
  const { delay: requestDelay = 1000, errorRate = 0, errorMessage = '请求失败' } = options;
  
  // 模拟网络延时
  await delay(requestDelay);
  
  // 模拟随机错误
  if (shouldSimulateError(errorRate)) {
    throw new Error(errorMessage);
  }
  
  return createSuccessResponse(data);
};
