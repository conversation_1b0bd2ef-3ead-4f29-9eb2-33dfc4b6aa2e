/**
 * 常量定义
 */

// 用户角色
export const USER_ROLES = {
  USER: 'user',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
} as const;

// 设备状态
export const DEVICE_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  MAINTENANCE: 'maintenance',
} as const;

// 实例状态
export const INSTANCE_STATUS = {
  RUNNING: 'running',
  STOPPED: 'stopped',
  PENDING: 'pending',
  ERROR: 'error',
} as const;

// 应用状态
export const APP_STATUS = {
  INSTALLING: 'installing',
  RUNNING: 'running',
  STOPPED: 'stopped',
  FAILED: 'failed',
} as const;

// 设备类型
export const DEVICE_TYPES = {
  GPU: 'GPU',
  CPU: 'CPU',
} as const;

// 分页默认配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// API 延时配置
export const API_DELAY = {
  FAST: 300,
  NORMAL: 1000,
  SLOW: 2000,
} as const;
