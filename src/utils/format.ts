/**
 * 格式化工具函数
 */

// 格式化价格
export const formatPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`;
};

// 格式化时间
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 格式化持续时间
export const formatDuration = (startTime: string, endTime?: string): string => {
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const diffMs = end.getTime() - start.getTime();
  
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }
  return `${minutes}分钟`;
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 B';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
};

// 格式化状态文本
export const formatStatus = (status: string): { text: string; color: string } => {
  const statusMap: Record<string, { text: string; color: string }> = {
    online: { text: '在线', color: 'green' },
    offline: { text: '离线', color: 'red' },
    maintenance: { text: '维护中', color: 'orange' },
    running: { text: '运行中', color: 'green' },
    stopped: { text: '已停止', color: 'red' },
    pending: { text: '启动中', color: 'orange' },
    error: { text: '错误', color: 'red' },
    installing: { text: '安装中', color: 'blue' },
    failed: { text: '失败', color: 'red' },
  };
  
  return statusMap[status] || { text: status, color: 'gray' };
};
