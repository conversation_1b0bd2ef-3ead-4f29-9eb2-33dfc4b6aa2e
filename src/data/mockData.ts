// 设备类型定义
export interface Device {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'maintenance';
  type: 'GPU' | 'CPU';
  cpu: string;
  memory: string;
  gpu: string;
  storage: string;
  location: string;
  ip: string;
  exclusive: boolean;
  pricePerHour: number;
  availability: {
    start: string;
    end: string;
  };
  owner: string;
  createdAt: string;
  provider: string;
  rating: number;
  totalOrders: number;
}

// 实例类型定义
export interface Instance {
  id: string;
  name: string;
  deviceId: string;
  status: 'running' | 'stopped' | 'pending' | 'error';
  cpu: string;
  memory: string;
  gpu: string;
  storage: string;
  pricePerHour: number;
  totalCost: number;
  startTime: string;
  endTime?: string;
  userId: string;
  applications: string[];
  applicationInfo?: {
    id: string;
    name: string;
    icon: string;
    version: string;
    status: 'installing' | 'running' | 'stopped' | 'failed';
    ports: number[];
    accessUrl?: string;
    pricePerHour?: number;
    installTime?: string;
  };
  sshInfo?: {
    host: string;
    port: number;
    username: string;
  };
}

// 应用类型定义
export interface Application {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  icon: string;
  version: string;
  requirements: {
    minCpu: number;
    minMemory: string;
    minGpu?: string;
    minStorage: string;
  };
  installCommand: string;
  ports: number[];
  isOfficial: boolean;
  downloads: number;
  rating: number;
  createdAt: string;
  pricePerHour?: number; // 应用价格（可选）
}

// Mock设备数据
export const mockDevices: Device[] = [
  {
    id: '1',
    name: 'AI训练服务器-01',
    status: 'online',
    type: 'GPU',
    cpu: 'AMD EPYC 32核',
    memory: '128 GB',
    gpu: '2×RTX 4090',
    storage: '2 TB NVMe',
    location: '上海',
    ip: '*************',
    exclusive: false,
    pricePerHour: 8.5,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user1',
    createdAt: '2024-01-15T10:00:00Z',
    provider: 'TechCloud',
    rating: 4.8,
    totalOrders: 156,
  },
  {
    id: '2',
    name: '渲染节点-04',
    status: 'online',
    type: 'GPU',
    cpu: 'Intel Xeon 16核',
    memory: '64 GB',
    gpu: 'RTX A6000',
    storage: '1 TB SSD',
    location: '北京',
    ip: '*************',
    exclusive: false,
    pricePerHour: 6.0,
    availability: { start: '08:00', end: '20:00' },
    owner: 'user2',
    createdAt: '2024-01-10T14:30:00Z',
    provider: 'CloudGPU',
    rating: 4.6,
    totalOrders: 89,
  },
  {
    id: '3',
    name: '数据分析服务器',
    status: 'online',
    type: 'CPU',
    cpu: 'AMD Threadripper 64核',
    memory: '256 GB',
    gpu: '无',
    storage: '8 TB HDD',
    location: '广州',
    ip: '*************',
    exclusive: false,
    pricePerHour: 4.2,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user3',
    createdAt: '2024-01-08T09:15:00Z',
    provider: 'DataCenter Pro',
    rating: 4.5,
    totalOrders: 234,
  },
  {
    id: '4',
    name: 'GPU计算节点-07',
    status: 'online',
    type: 'GPU',
    cpu: 'Intel i9 16核',
    memory: '64 GB',
    gpu: '4×RTX 3090',
    storage: '4 TB NVMe',
    location: '上海',
    ip: '*************',
    exclusive: false,
    pricePerHour: 12.0,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user4',
    createdAt: '2024-01-12T16:45:00Z',
    provider: 'AI Computing',
    rating: 4.9,
    totalOrders: 67,
  },
  {
    id: '5',
    name: 'Web编译服务器',
    status: 'online',
    type: 'CPU',
    cpu: 'Intel Xeon 32核',
    memory: '64 GB',
    gpu: '无',
    storage: '512 GB NVMe',
    location: '北京',
    ip: '************',
    exclusive: false,
    pricePerHour: 3.5,
    availability: { start: '06:00', end: '22:00' },
    owner: 'user5',
    createdAt: '2024-01-05T11:20:00Z',
    provider: 'DevCloud',
    rating: 4.3,
    totalOrders: 178,
  },
  {
    id: '6',
    name: '深度学习工作站',
    status: 'online',
    type: 'GPU',
    cpu: 'AMD Ryzen 9 16核',
    memory: '128 GB',
    gpu: '8×RTX 4080',
    storage: '2 TB NVMe',
    location: '深圳',
    ip: '*************',
    exclusive: false,
    pricePerHour: 15.8,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user6',
    createdAt: '2024-01-20T08:30:00Z',
    provider: 'ML Labs',
    rating: 4.7,
    totalOrders: 45,
  },
  {
    id: '7',
    name: '高性能计算集群',
    status: 'online',
    type: 'CPU',
    cpu: 'Intel Xeon Platinum 96核',
    memory: '512 GB',
    gpu: '无',
    storage: '16 TB SSD',
    location: '杭州',
    ip: '*************',
    exclusive: false,
    pricePerHour: 7.2,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user7',
    createdAt: '2024-01-18T12:15:00Z',
    provider: 'HPC Solutions',
    rating: 4.4,
    totalOrders: 123,
  },
  {
    id: '8',
    name: '游戏渲染农场',
    status: 'online',
    type: 'GPU',
    cpu: 'Intel i7 12核',
    memory: '32 GB',
    gpu: '2×RTX 3070',
    storage: '1 TB NVMe',
    location: '成都',
    ip: '*************',
    exclusive: false,
    pricePerHour: 5.5,
    availability: { start: '10:00', end: '22:00' },
    owner: 'user8',
    createdAt: '2024-01-22T14:20:00Z',
    provider: 'GameCloud',
    rating: 4.2,
    totalOrders: 78,
  },
  {
    id: '9',
    name: '科学计算节点',
    status: 'online',
    type: 'GPU',
    cpu: 'AMD EPYC 48核',
    memory: '256 GB',
    gpu: '4×RTX A5000',
    storage: '8 TB NVMe',
    location: '西安',
    ip: '*************',
    exclusive: false,
    pricePerHour: 11.5,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user9',
    createdAt: '2024-01-25T09:45:00Z',
    provider: 'SciCompute',
    rating: 4.6,
    totalOrders: 92,
  },
  {
    id: '10',
    name: '边缘计算节点',
    status: 'online',
    type: 'CPU',
    cpu: 'ARM Neoverse 32核',
    memory: '64 GB',
    gpu: '无',
    storage: '1 TB SSD',
    location: '武汉',
    ip: '*************',
    exclusive: false,
    pricePerHour: 2.8,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user10',
    createdAt: '2024-01-28T16:30:00Z',
    provider: 'EdgeTech',
    rating: 4.1,
    totalOrders: 156,
  },
  {
    id: '11',
    name: 'AI推理服务器',
    status: 'online',
    type: 'GPU',
    cpu: 'Intel Xeon 24核',
    memory: '96 GB',
    gpu: '6×RTX 4070',
    storage: '3 TB NVMe',
    location: '南京',
    ip: '*************',
    exclusive: false,
    pricePerHour: 9.8,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user11',
    createdAt: '2024-01-30T11:10:00Z',
    provider: 'InferenceCloud',
    rating: 4.5,
    totalOrders: 134,
  },
  {
    id: '12',
    name: '区块链挖矿节点',
    status: 'maintenance',
    type: 'GPU',
    cpu: 'AMD Ryzen 7 8核',
    memory: '32 GB',
    gpu: '12×RTX 3060',
    storage: '500 GB SSD',
    location: '青岛',
    ip: '*************',
    exclusive: false,
    pricePerHour: 4.5,
    availability: { start: '00:00', end: '23:59' },
    owner: 'user12',
    createdAt: '2024-02-01T13:25:00Z',
    provider: 'CryptoMine',
    rating: 3.9,
    totalOrders: 67,
  },
];

// Mock实例数据
export const mockInstances: Instance[] = [
  {
    id: 'inst-1',
    name: 'DeepSeek训练实例',
    deviceId: '1',
    status: 'running',
    cpu: 'AMD EPYC 16核',
    memory: '64 GB',
    gpu: '1×RTX 4090',
    storage: '1 TB NVMe',
    pricePerHour: 5.45, // 设备4.25 + 应用1.2
    totalCost: 130.8,
    startTime: '2024-01-18T08:00:00Z',
    userId: '1',
    applications: ['app-1'],
    applicationInfo: {
      id: 'app-1',
      name: 'DeepSeek Coder',
      icon: '🤖',
      version: '1.5.0',
      status: 'running',
      ports: [8080],
      accessUrl: 'http://*************:8080',
      pricePerHour: 1.2,
      installTime: '2024-01-18T08:05:00Z',
    },
    sshInfo: {
      host: '*************',
      port: 22001,
      username: 'user1',
    },
  },
  {
    id: 'inst-2',
    name: 'Stable Diffusion实例',
    deviceId: '2',
    status: 'running',
    cpu: 'Intel Xeon 16核',
    memory: '64 GB',
    gpu: 'RTX A6000',
    storage: '1 TB SSD',
    pricePerHour: 8.5, // 设备6.0 + 应用2.5
    totalCost: 204.0,
    startTime: '2024-01-17T14:00:00Z',
    userId: '1',
    applications: ['app-6'],
    applicationInfo: {
      id: 'app-6',
      name: 'Stable Diffusion WebUI',
      icon: '🎨',
      version: '1.7.0',
      status: 'running',
      ports: [7860],
      accessUrl: 'http://*************:7860',
      pricePerHour: 2.5,
      installTime: '2024-01-17T14:10:00Z',
    },
    sshInfo: {
      host: '*************',
      port: 22002,
      username: 'user1',
    },
  },
];

// Mock应用数据
export const mockApplications: Application[] = [
  {
    id: 'app-1',
    name: 'DeepSeek Coder',
    description: '强大的AI代码生成和补全工具，支持多种编程语言',
    category: '开发工具',
    tags: ['AI', 'Code Generation', 'DeepSeek'],
    icon: '🤖',
    version: '1.5.0',
    requirements: {
      minCpu: 8,
      minMemory: '16 GB',
      minGpu: 'RTX 3060',
      minStorage: '50 GB',
    },
    installCommand: 'docker run -d -p 8080:8080 deepseek/coder:latest',
    ports: [8080],
    isOfficial: true,
    downloads: 15420,
    rating: 4.8,
    createdAt: '2024-01-01T00:00:00Z',
    pricePerHour: 1.2, // 付费应用
  },
  {
    id: 'app-2',
    name: 'Blender Render Farm',
    description: '专业的3D渲染农场，支持分布式渲染',
    category: '图形渲染',
    tags: ['3D', 'Rendering', 'Blender'],
    icon: '🎨',
    version: '4.0.2',
    requirements: {
      minCpu: 16,
      minMemory: '32 GB',
      minGpu: 'RTX 3070',
      minStorage: '100 GB',
    },
    installCommand: 'docker run -d -p 8081:8081 blender/render-farm:latest',
    ports: [8081],
    isOfficial: true,
    downloads: 8930,
    rating: 4.6,
    createdAt: '2024-01-05T00:00:00Z',
  },
  {
    id: 'app-3',
    name: 'Jupyter Lab',
    description: '交互式开发环境，支持Python、R等多种语言',
    category: '开发工具',
    tags: ['Python', 'Data Science', 'Jupyter'],
    icon: '📊',
    version: '3.6.1',
    requirements: {
      minCpu: 4,
      minMemory: '8 GB',
      minStorage: '20 GB',
    },
    installCommand: 'docker run -d -p 8888:8888 jupyter/lab:latest',
    ports: [8888],
    isOfficial: true,
    downloads: 25680,
    rating: 4.9,
    createdAt: '2024-01-03T00:00:00Z',
  },
  {
    id: 'app-4',
    name: 'Open WebUI',
    description: '开源的ChatGPT风格Web界面，支持多种AI模型',
    category: 'AI工具',
    tags: ['AI', 'Chat', 'Open Source'],
    icon: '💬',
    version: '0.1.105',
    requirements: {
      minCpu: 2,
      minMemory: '4 GB',
      minStorage: '10 GB',
    },
    installCommand:
      'docker run -d -p 3000:8080 ghcr.io/open-webui/open-webui:main',
    ports: [3000],
    isOfficial: false,
    downloads: 12340,
    rating: 4.7,
    createdAt: '2024-01-10T00:00:00Z',
  },
  {
    id: 'app-5',
    name: 'TensorFlow Serving',
    description: '高性能机器学习模型服务系统',
    category: 'AI工具',
    tags: ['TensorFlow', 'ML', 'Serving'],
    icon: '🧠',
    version: '2.14.0',
    requirements: {
      minCpu: 8,
      minMemory: '16 GB',
      minGpu: 'RTX 3060',
      minStorage: '50 GB',
    },
    installCommand: 'docker run -d -p 8501:8501 tensorflow/serving:latest',
    ports: [8501],
    isOfficial: true,
    downloads: 18750,
    rating: 4.5,
    createdAt: '2024-01-12T00:00:00Z',
  },
  {
    id: 'app-6',
    name: 'Stable Diffusion WebUI',
    description: 'AI图像生成工具，支持多种模型',
    category: '图形渲染',
    tags: ['AI', 'Image Generation', 'Stable Diffusion'],
    icon: '🎨',
    version: '1.7.0',
    requirements: {
      minCpu: 8,
      minMemory: '16 GB',
      minGpu: 'RTX 3070',
      minStorage: '50 GB',
    },
    installCommand: 'docker run -d -p 7860:7860 sd-webui:latest',
    ports: [7860],
    isOfficial: false,
    downloads: 35600,
    rating: 4.9,
    createdAt: '2024-01-16T00:00:00Z',
    pricePerHour: 2.5, // 付费应用
  },
  {
    id: 'app-7',
    name: 'Apache Spark',
    description: '大数据处理引擎，支持批处理和流处理',
    category: '数据分析',
    tags: ['Big Data', 'Spark', 'Analytics'],
    icon: '📊',
    version: '3.5.0',
    requirements: {
      minCpu: 16,
      minMemory: '64 GB',
      minStorage: '200 GB',
    },
    installCommand: 'docker run -d -p 8080:8080 apache/spark:latest',
    ports: [8080, 7077],
    isOfficial: true,
    downloads: 14200,
    rating: 4.4,
    createdAt: '2024-01-18T00:00:00Z',
  },
  {
    id: 'app-8',
    name: 'Minecraft Server',
    description: '我的世界游戏服务器',
    category: '游戏服务',
    tags: ['Game', 'Minecraft', 'Server'],
    icon: '🎮',
    version: '1.20.4',
    requirements: {
      minCpu: 4,
      minMemory: '8 GB',
      minStorage: '10 GB',
    },
    installCommand: 'docker run -d -p 25565:25565 itzg/minecraft-server:latest',
    ports: [25565],
    isOfficial: false,
    downloads: 28900,
    rating: 4.6,
    createdAt: '2024-01-22T00:00:00Z',
  },
  {
    id: 'app-9',
    name: 'Redis Cluster',
    description: '高性能内存数据库集群',
    category: '数据库',
    tags: ['Database', 'Cache', 'Redis'],
    icon: '🗄️',
    version: '7.2.0',
    requirements: {
      minCpu: 8,
      minMemory: '32 GB',
      minStorage: '100 GB',
    },
    installCommand: 'docker run -d -p 6379:6379 redis:latest',
    ports: [6379],
    isOfficial: true,
    downloads: 16700,
    rating: 4.7,
    createdAt: '2024-01-24T00:00:00Z',
  },
  {
    id: 'app-10',
    name: 'Ollama',
    description: '本地大语言模型运行环境',
    category: 'AI工具',
    tags: ['LLM', 'Local AI', 'Ollama'],
    icon: '🤖',
    version: '0.1.26',
    requirements: {
      minCpu: 8,
      minMemory: '16 GB',
      minGpu: 'RTX 4060',
      minStorage: '50 GB',
    },
    installCommand: 'docker run -d -p 11434:11434 ollama/ollama:latest',
    ports: [11434],
    isOfficial: false,
    downloads: 21400,
    rating: 4.8,
    createdAt: '2024-01-26T00:00:00Z',
    pricePerHour: 1.8, // 付费应用
  },
];
