# SailFusion Cloud 帆一异构智算云平台原型 Web 应用（纯前端原型）

这是一个帆一异构智算云平台的**纯前端原型 Web 应用**。此项目**无需任何后端逻辑或数据库**，所有数据将通过本地的模拟数据 (Mock Data) 文件提供。此原型旨在完整地展示核心用户流程，包括用户租用实例、提供方托管实例以及管理员进行后台管理。重点在于 UI/UX 的流畅性和交互的真实感。（类似 Vast.ai、RunPod 等）

## 产品逻辑

### 用户

1. 用户注册（普通用户）
2. 租用实例（算力市场）
   1. 可以根据各类参数筛选实例
   2. 可以根据应用筛选实例（应用对机器有最低闲置，这种筛选下，订购完成后，询问用户是否自动安装该应用）
   3. AI 推荐实例（通过引导+多轮对话明确用户需求，然后展示推荐的实例信息，用户可以直接订购）
   4. 订购时，可以定制设备配置，比如 8 卡设备，用户可选 1 ～ 8 卡，硬盘同理，然后生成实例。
      1. 选择 2 卡后，下次该设备只可选择 6 卡
3. 实例管理（我的实例）
   1. 展示实例的详细配置、运行状态等。
   2. 查看资源使用监控（含计费信息）
   3. 应用：应用面板（如访问地址等信息）、安装新应用（替换当前镜像 / 增量安装，细节待定）
   4. 操作：SSH 连接、退订
4. 应用管理（应用市场）
   1. 卡片展示，可以根据分类（如开发、绘图、工业应用等）、名称、标签（DeepSeek 等特点）进行筛选
   2. 支持一键部署，选择现有实例、托管设备（未上架的），或过滤可购买的实例（注意，实例可能不满足应用需求，需要有相关提示）
   3. 个人模板（可以先不做，后续可以从用户当前的实例镜像直接生成）
5. 个人相关
   1. 账单
   2. 团队（低优先级）。如果用户没有升级为团队，可以点击升级为团队（付费点）。升级为团队后，可以添加团队成员（可以邀请用户加入），分配可操作实例，转让管理员权限。
   3. 个人设置
6. 设备托管（查询到用户有托管的设备后，按钮可以换一个名字，比如“我的设备”）
   1. 可以通过 SSH （自动）/ 脚本（手动，提供操作文档）接入设备
      1. 定价。提供 AI 辅助定价功能
   2. 可以查看所有的设备。设备状态分为：未上架（自用）、已上架（出租）。租售中（不同的概念）
   3. 租售/下架，可以切换设备状态
7. 多语言切换（简体中文、繁体中文、英文、泰文。暂不重要，不做）

### 超管

1. 设备管理
   1. 统一的列表，展示各类指标、资源占用情况、计费收益等。可以查看设备下的实例状态等（一个设备可以拆分为多个实例）
   2. 设备集群接入，按钮。（相当于企业级的设备托管，接入后会分配企业租户，然后把对应的企业账号给到企业。企业登录后，可以进行出租等常规流程）
   3. 操作项：
      1. 设备：强制下架；分配租户
      2. 实例：SSH 连接（异常维护）、查看日志
2. 应用模板管理
   1. 展示所有应用模板，类似上面的应用管理功能，但是新增超管相关功能。如上架、下架、价格配置、新增、编辑、删除等
   2. 前期做四个，开发环境、裸模型、开源应用（Open WebUI）、帆一应用（Halo 可视化）
3. 分润管理
   1. 列表展示所有分润规则
   2. 分润规则可以应用在全局、团队、实例、应用，优先级逐步提高，以实例的分润规则为准（前期可以固定全局分润）。实例具体的分润从实例、团队、全局逐级获取。

## 技术栈

1. React + Javascript, 部分类型定义及工具函数使用 Typescript
2. Tailwind CSS
3. Vite
4. Shadcn UI
5. Radix UI / Ant Design
6. ahooks + axios

## 当前实现状态

### 算力市场页面 (ComputeMarket.jsx)

**已实现功能：**

- ✅ 页面已转换为 JSX 格式（从 TypeScript 转换为 JavaScript）
- ✅ 新增 CPU 筛选功能，支持从接口动态获取 CPU 型号
- ✅ 地理位置筛选已改为从接口获取区域数据
- ✅ GPU 筛选已改为从接口获取 GPU 型号数据
- ✅ 使用 ahooks 的 useRequest 钩子进行 API 请求管理
- ✅ 集成了错误处理和加载状态显示

**API 服务层：**

- ✅ `src/services/gpu.js` - GPU 枚举接口
- ✅ `src/services/cpu.js` - CPU 枚举接口
- ✅ `src/services/region.js` - 区域枚举接口

**接口对接：**

- ✅ GPU 枚举：`GET /ihmp/gpu`
- ✅ CPU 枚举：`GET /ihmp/cpu`
- ✅ 区域枚举：`GET /ihmp/region`

**筛选功能：**

- ✅ 设备类型筛选（全部/GPU/CPU）
- ✅ 地理位置筛选（从接口获取区域列表）
- ✅ GPU 型号筛选（从接口获取 GPU 列表）
- ✅ CPU 型号筛选（从接口获取 CPU 列表）
- ✅ 应用场景筛选
- ✅ 搜索功能
- ✅ 筛选条件重置功能

**技术特点：**

- 使用 ahooks 的 useRequest 进行接口请求
- 支持加载状态和错误处理
- 响应式设计，支持移动端
- 实时筛选和搜索
